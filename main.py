# -*- coding: utf-8 -*-
"""
نظام صيدلية الشفاء - الملف الرئيسي العامل
"""

import tkinter as tk
from tkinter import messagebox
import mysql.connector
from datetime import datetime
import os
import subprocess
import platform

# مكتبات PDF
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.pdfgen import canvas
    from reportlab.lib.units import inch
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.lib.colors import black, blue, green
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    print("تحذير: مكتبة reportlab غير مثبتة. سيتم استخدام الطباعة النصية.")

# الألوان والخطوط المحسنة
COLORS = {
    'primary': '#27ae60',
    'secondary': '#2c3e50',
    'bg_main': '#f8f9fa',
    'bg_card': '#ffffff',
    'bg_sidebar': '#2c3e50',
    'text_primary': '#2c3e50',
    'text_secondary': '#34495e',
    'text_light': '#7f8c8d',
    'text_white': '#ffffff',
    'btn_success': '#27ae60',
    'btn_danger': '#e74c3c',
    'btn_warning': '#f39c12',
    'btn_info': '#17a2b8',
    'accent': '#3498db',
    'error': '#e74c3c',
    'warning': '#f39c12',
    'info': '#17a2b8',
    'light': '#f8f9fa',
    'dark': '#343a40',
    'gradient_start': '#667eea',
    'gradient_end': '#764ba2'
}

def get_responsive_fonts():
    """الحصول على خطوط متجاوبة بناءً على حجم الشاشة"""
    import tkinter as tk
    temp_root = tk.Tk()
    screen_width = temp_root.winfo_screenwidth()
    temp_root.destroy()

    if screen_width >= 1920:  # شاشة كبيرة
        return {
            'title': ('Arial', 24, 'bold'),
            'heading': ('Arial', 18, 'bold'),
            'subheading': ('Arial', 16, 'bold'),
            'main': ('Arial', 14),
            'small': ('Arial', 12),
            'button': ('Arial', 13, 'bold'),
            'large_button': ('Arial', 15, 'bold')
        }
    elif screen_width >= 1366:  # شاشة متوسطة
        return {
            'title': ('Arial', 20, 'bold'),
            'heading': ('Arial', 16, 'bold'),
            'subheading': ('Arial', 14, 'bold'),
            'main': ('Arial', 12),
            'small': ('Arial', 10),
            'button': ('Arial', 11, 'bold'),
            'large_button': ('Arial', 13, 'bold')
        }
    else:  # شاشة صغيرة
        return {
            'title': ('Arial', 16, 'bold'),
            'heading': ('Arial', 14, 'bold'),
            'subheading': ('Arial', 12, 'bold'),
            'main': ('Arial', 10),
            'small': ('Arial', 9),
            'button': ('Arial', 10, 'bold'),
            'large_button': ('Arial', 11, 'bold')
        }

# تطبيق الخطوط المتجاوبة
FONTS = get_responsive_fonts()

# الرموز والأيقونات
ICONS = {
    'pharmacy': '🏥',
    'dashboard': '📊',
    'inventory': '📦',
    'sales': '🛒',
    'invoice': '🧾',
    'users': '👥',
    'logout': '🚪',
    'add': '➕',
    'edit': '✏️',
    'delete': '🗑️',
    'search': '🔍',
    'print': '🖨️',
    'save': '💾',
    'refresh': '🔄',
    'settings': '⚙️',
    'help': '❓',
    'success': '✅',
    'warning': '⚠️',
    'error': '❌',
    'info': 'ℹ️'
}

# متغير المستخدم الحالي
current_user = {'username': '', 'role': ''}

def connect_db():
    """الاتصال بقاعدة البيانات"""
    try:
        return mysql.connector.connect(
            host="localhost",
            user="root",
            password="",
            database="pharmacy_db"
        )
    except:
        return None

def login():
    """تسجيل الدخول مع تأثيرات بصرية"""
    username = username_entry.get().strip()
    password = password_entry.get().strip()

    if not username or not password:
        messagebox.showerror(f"{ICONS['error']} خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
        return

    # تأثيرات بصرية أثناء المعالجة (سيتم تطبيقها لاحقاً)
    # login_btn.configure(text=f"{ICONS['refresh']} جاري التحقق...", state="disabled")
    # root.update()
    
    try:
        db = connect_db()
        if not db:
            messagebox.showerror("خطأ", "فشل في الاتصال بقاعدة البيانات")
            return
            
        cursor = db.cursor()
        cursor.execute("SELECT username, role FROM users WHERE username = %s AND password = %s", 
                      (username, password))
        result = cursor.fetchone()
        cursor.close()
        db.close()
        
        if result:
            current_user['username'] = result[0]
            current_user['role'] = result[1]

            role_name = "مدير النظام" if result[1] == 'admin' else "مستخدم محدود"
            role_icon = ICONS['settings'] if result[1] == 'admin' else ICONS['users']

            messagebox.showinfo(f"{ICONS['success']} مرحباً",
                              f"{role_icon} أهلاً وسهلاً {result[0]}\n"
                              f"✅ تم تسجيل الدخول بنجاح كـ {role_name}\n"
                              f"🏥 مرحباً بك في صيدلية الشفاء")

            show_main_system()
        else:
            messagebox.showerror(f"{ICONS['error']} خطأ في تسجيل الدخول",
                               f"❌ اسم المستخدم أو كلمة المرور غير صحيحة\n"
                               f"💡 تأكد من البيانات وحاول مرة أخرى")
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

def show_main_system():
    """عرض النظام الرئيسي المحسن"""
    # إخفاء شاشة تسجيل الدخول
    login_frame.pack_forget()

    # إنشاء الشريط العلوي المحسن
    top_frame = tk.Frame(root, bg=COLORS['primary'], height=70)
    top_frame.pack(side="top", fill="x")
    top_frame.pack_propagate(False)

    # إطار العنوان مع أيقونة
    title_frame = tk.Frame(top_frame, bg=COLORS['primary'])
    title_frame.pack(side="left", padx=20, pady=15)

    tk.Label(title_frame, text=f"{ICONS['pharmacy']} صيدلية الشفاء",
             font=FONTS['title'], bg=COLORS['primary'], fg=COLORS['text_white']).pack(side="left")

    tk.Label(title_frame, text="نظام إدارة شامل ومتطور",
             font=FONTS['small'], bg=COLORS['primary'], fg=COLORS['text_white']).pack(side="left", padx=(10, 0))

    # إطار معلومات المستخدم المحسن
    user_frame = tk.Frame(top_frame, bg=COLORS['primary'])
    user_frame.pack(side="right", padx=20, pady=15)

    role_name = "مدير النظام" if current_user['role'] == 'admin' else "مستخدم محدود"
    role_icon = ICONS['settings'] if current_user['role'] == 'admin' else ICONS['users']

    tk.Label(user_frame, text=f"{role_icon} {current_user['username']}",
             font=FONTS['subheading'], bg=COLORS['primary'], fg=COLORS['text_white']).pack()

    tk.Label(user_frame, text=f"({role_name})",
             font=FONTS['small'], bg=COLORS['primary'], fg=COLORS['text_white']).pack()
    
    # إنشاء القائمة الجانبية المتجاوبة
    # تحديد عرض القائمة بناءً على حجم الشاشة
    screen_width = root.winfo_screenwidth()
    if screen_width >= 1920:
        sidebar_width = 300
    elif screen_width >= 1366:
        sidebar_width = 250
    else:
        sidebar_width = 200

    sidebar = tk.Frame(root, bg=COLORS['bg_sidebar'], width=sidebar_width)
    sidebar.pack(side="left", fill="y")
    sidebar.pack_propagate(False)

    # شعار محسن في القائمة
    logo_frame = tk.Frame(sidebar, bg=COLORS['bg_sidebar'])
    logo_frame.pack(pady=30)

    tk.Label(logo_frame, text=f"{ICONS['pharmacy']}", font=('Arial', 24),
             bg=COLORS['bg_sidebar'], fg=COLORS['primary']).pack()

    tk.Label(logo_frame, text="صيدلية الشفاء", font=FONTS['title'],
             bg=COLORS['bg_sidebar'], fg=COLORS['text_white']).pack(pady=(5, 0))

    tk.Label(logo_frame, text="نظام إدارة متطور", font=FONTS['small'],
             bg=COLORS['bg_sidebar'], fg=COLORS['accent']).pack()

    # خط فاصل
    separator = tk.Frame(sidebar, height=2, bg=COLORS['accent'])
    separator.pack(fill="x", padx=20, pady=20)

    # أزرار القائمة المحسنة مع أيقونات
    buttons = [
        (f"{ICONS['dashboard']} لوحة التحكم", show_dashboard, COLORS['info']),
        (f"{ICONS['inventory']} إدارة المخزون", show_inventory, COLORS['warning']),
        (f"{ICONS['sales']} إدارة المبيعات", show_sales, COLORS['primary']),
    ]

    # أزرار المدير فقط
    if current_user['role'] == 'admin':
        buttons.extend([
            (f"{ICONS['invoice']} إدارة الفواتير", show_invoices, COLORS['accent']),
            (f"{ICONS['users']} إدارة المستخدمين", show_users, COLORS['btn_info']),
        ])

    buttons.extend([
        (f"{ICONS['settings']} اختبار قاعدة البيانات", test_database, COLORS['dark']),
        (f"{ICONS['logout']} تسجيل خروج", logout, COLORS['btn_danger'])
    ])

    # إنشاء الأزرار المحسنة
    for text, command, color in buttons:
        btn_frame = tk.Frame(sidebar, bg=COLORS['bg_sidebar'])
        btn_frame.pack(fill="x", pady=3, padx=15)

        btn = tk.Button(btn_frame, text=text, command=command,
                       bg=COLORS['bg_sidebar'], fg=COLORS['text_white'],
                       font=FONTS['main'], relief="flat", anchor="w",
                       padx=15, pady=12, bd=0)
        btn.pack(fill="x")

        # تأثير hover محسن
        def on_enter(event, button=btn, hover_color=color):
            button.configure(bg=hover_color, relief="raised", bd=1)

        def on_leave(event, button=btn):
            button.configure(bg=COLORS['bg_sidebar'], relief="flat", bd=0)

        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
    
    # إنشاء المحتوى الرئيسي
    global main_content
    main_content = tk.Frame(root, bg=COLORS['bg_main'])
    main_content.pack(side="right", fill="both", expand=True)
    
    # عرض لوحة التحكم افتراضياً
    show_dashboard()

def clear_content():
    """مسح المحتوى الرئيسي"""
    for widget in main_content.winfo_children():
        widget.destroy()

def show_dashboard():
    """عرض لوحة التحكم المحسنة"""
    clear_content()

    # إطار العنوان المحسن
    header_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    header_frame.pack(fill="x", pady=20, padx=30)

    # عنوان الصفحة مع أيقونة
    title_frame = tk.Frame(header_frame, bg=COLORS['bg_main'])
    title_frame.pack(side="left")

    tk.Label(title_frame, text=f"{ICONS['dashboard']} لوحة التحكم",
             font=FONTS['title'], bg=COLORS['bg_main'], fg=COLORS['primary']).pack(side="left")

    # الوقت والتاريخ
    from datetime import datetime
    now = datetime.now()
    time_frame = tk.Frame(header_frame, bg=COLORS['bg_main'])
    time_frame.pack(side="right")

    tk.Label(time_frame, text=now.strftime("%Y-%m-%d"),
             font=FONTS['subheading'], bg=COLORS['bg_main'], fg=COLORS['text_primary']).pack()
    tk.Label(time_frame, text=now.strftime("%H:%M:%S"),
             font=FONTS['main'], bg=COLORS['bg_main'], fg=COLORS['accent']).pack()

    # رسالة ترحيب محسنة
    welcome_frame = tk.Frame(main_content, bg=COLORS['bg_card'], relief="raised", bd=2)
    welcome_frame.pack(fill="x", pady=10, padx=30)

    tk.Label(welcome_frame, text=f"{ICONS['success']} مرحباً بك {current_user['username']}",
             font=FONTS['heading'], bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=15)

    role_name = "مدير النظام" if current_user['role'] == 'admin' else "مستخدم محدود"
    tk.Label(welcome_frame, text=f"تم تسجيل الدخول بنجاح كـ {role_name}",
             font=FONTS['main'], bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(pady=(0, 15))

    # إحصائيات محسنة
    stats_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    stats_frame.pack(pady=20, padx=30, fill="x")
    
    try:
        db = connect_db()
        if db:
            cursor = db.cursor()
            
            # عدد المنتجات
            cursor.execute("SELECT COUNT(*) FROM inventory")
            products_count = cursor.fetchone()[0]
            
            # عدد المبيعات
            cursor.execute("SELECT COUNT(*) FROM sales")
            sales_count = cursor.fetchone()[0]
            
            # عدد المستخدمين
            cursor.execute("SELECT COUNT(*) FROM users")
            users_count = cursor.fetchone()[0]
            
            cursor.close()
            db.close()
            
            # بطاقات الإحصائيات الجذابة
            stats = [
                (f"{ICONS['inventory']} المنتجات", products_count, COLORS['warning']),
                (f"{ICONS['sales']} المبيعات", sales_count, COLORS['info']),
                (f"{ICONS['users']} المستخدمين", users_count, COLORS['accent'])
            ]

            # تحديد عدد الأعمدة بناءً على حجم الشاشة
            screen_width = root.winfo_screenwidth()
            if screen_width >= 1920:
                max_cols = 4
                card_padx = 20
            elif screen_width >= 1366:
                max_cols = 3
                card_padx = 15
            else:
                max_cols = 2
                card_padx = 10

            for i, (label, value, color) in enumerate(stats):
                # إطار البطاقة مع تأثيرات بصرية
                stat_frame = tk.Frame(stats_frame, bg=COLORS['bg_card'], relief="raised", bd=3)

                # ترتيب البطاقات في صفوف وأعمدة للشاشات الصغيرة
                if len(stats) > max_cols:
                    row = i // max_cols
                    col = i % max_cols
                    stat_frame.grid(row=row, column=col, padx=card_padx, pady=10, sticky="nsew")
                    # جعل الأعمدة قابلة للتوسع
                    stats_frame.grid_columnconfigure(col, weight=1)
                else:
                    stat_frame.pack(side="left", padx=card_padx, pady=10, fill="both", expand=True)

                # شريط علوي ملون
                top_bar = tk.Frame(stat_frame, bg=color, height=5)
                top_bar.pack(fill="x")

                # محتوى البطاقة
                content_frame = tk.Frame(stat_frame, bg=COLORS['bg_card'])
                content_frame.pack(fill="both", expand=True, padx=15, pady=15)

                # القيمة الرئيسية
                tk.Label(content_frame, text=str(value), font=('Arial', 28, 'bold'),
                        bg=COLORS['bg_card'], fg=color).pack()

                # التسمية
                tk.Label(content_frame, text=label, font=FONTS['subheading'],
                        bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(pady=(5, 0))

                # تأثير hover للبطاقات
                def on_enter(event, frame=stat_frame):
                    frame.configure(relief="solid", bd=2)

                def on_leave(event, frame=stat_frame):
                    frame.configure(relief="raised", bd=3)

                stat_frame.bind("<Enter>", on_enter)
                stat_frame.bind("<Leave>", on_leave)

    except Exception as e:
        error_frame = tk.Frame(stats_frame, bg=COLORS['bg_card'], relief="raised", bd=2)
        error_frame.pack(fill="x", padx=15, pady=10)

        tk.Label(error_frame, text=f"{ICONS['error']} خطأ في تحميل الإحصائيات",
                font=FONTS['subheading'], bg=COLORS['bg_card'], fg=COLORS['error']).pack(pady=10)
        tk.Label(error_frame, text=str(e),
                font=FONTS['small'], bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(pady=(0, 10))

    # قائمة الوصول السريع
    quick_access_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    quick_access_frame.pack(pady=30, padx=30, fill="x")

    # عنوان القائمة
    tk.Label(quick_access_frame, text=f"{ICONS['settings']} الوصول السريع",
             font=FONTS['heading'], bg=COLORS['bg_main'], fg=COLORS['primary']).pack(pady=(0, 15))

    # إطار الأزرار
    buttons_frame = tk.Frame(quick_access_frame, bg=COLORS['bg_main'])
    buttons_frame.pack()

    # أزرار الوصول السريع
    quick_buttons = [
        (f"{ICONS['inventory']} إدارة المخزون", show_inventory, COLORS['warning']),
        (f"{ICONS['sales']} إدارة المبيعات", show_sales, COLORS['primary']),
        (f"{ICONS['invoice']} إدارة الفواتير", show_invoices, COLORS['info']),
        (f"{ICONS['refresh']} تحديث البيانات", lambda: refresh_dashboard(), COLORS['accent'])
    ]

    # تحديد تخطيط الأزرار بناءً على حجم الشاشة
    screen_width = root.winfo_screenwidth()
    if screen_width >= 1920:
        buttons_per_row = 4
        button_width = 18
    elif screen_width >= 1366:
        buttons_per_row = 4
        button_width = 15
    else:
        buttons_per_row = 2
        button_width = 20

    for i, (text, command, color) in enumerate(quick_buttons):
        row = i // buttons_per_row
        col = i % buttons_per_row

        btn = tk.Button(buttons_frame, text=text, command=command,
                       bg=color, fg=COLORS['text_white'],
                       font=FONTS['button'], relief="raised", bd=2,
                       padx=15, pady=8, width=button_width)
        btn.grid(row=row, column=col, padx=8, pady=5, sticky="ew")

        # جعل الأعمدة قابلة للتوسع
        buttons_frame.grid_columnconfigure(col, weight=1)

        # تأثير hover
        def on_enter(event, button=btn, hover_color=color):
            button.configure(bg=hover_color, relief="solid")

        def on_leave(event, button=btn, original_color=color):
            button.configure(bg=original_color, relief="raised")

        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)

def refresh_dashboard():
    """تحديث لوحة التحكم مع تأثيرات بصرية"""
    show_dashboard()
    messagebox.showinfo(f"{ICONS['refresh']} تحديث البيانات",
                       f"{ICONS['success']} تم تحديث البيانات بنجاح!\n"
                       f"📊 الإحصائيات محدثة\n"
                       f"🔄 البيانات الحديثة متاحة الآن")

def show_inventory():
    """عرض المخزون الكامل"""
    clear_content()

    # عنوان الصفحة
    title_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    title_frame.pack(fill="x", pady=10)

    tk.Label(title_frame, text="إدارة المخزون", font=FONTS['title'],
             bg=COLORS['bg_main'], fg=COLORS['primary']).pack(side="left", padx=20)

    # زر إضافة منتج جديد
    add_btn = tk.Button(title_frame, text="+ إضافة منتج جديد", command=add_product_window,
                       bg=COLORS['btn_success'], fg=COLORS['text_white'], font=FONTS['button'])
    add_btn.pack(side="right", padx=20)

    # إطار البحث
    search_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    search_frame.pack(fill="x", padx=20, pady=10)

    tk.Label(search_frame, text="البحث:", font=FONTS['main'], bg=COLORS['bg_main']).pack(side="left")
    search_entry = tk.Entry(search_frame, font=FONTS['main'], width=30)
    search_entry.pack(side="left", padx=10)

    search_btn = tk.Button(search_frame, text="بحث", command=lambda: search_products(search_entry.get()),
                          bg=COLORS['accent'], fg=COLORS['text_white'], font=FONTS['button'])
    search_btn.pack(side="left", padx=5)

    refresh_btn = tk.Button(search_frame, text="تحديث", command=show_inventory,
                           bg=COLORS['primary'], fg=COLORS['text_white'], font=FONTS['button'])
    refresh_btn.pack(side="left", padx=5)

    # إطار الجدول
    table_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    table_frame.pack(fill="both", expand=True, padx=20, pady=10)

    # إنشاء الجدول
    create_inventory_table(table_frame)

def create_inventory_table(parent):
    """إنشاء جدول المخزون"""
    from tkinter import ttk

    # إنشاء Treeview
    columns = ('اسم المنتج', 'الفئة', 'سعر الشراء', 'سعر البيع', 'الكمية', 'تاريخ الانتهاء')
    tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)

    # تعريف العناوين
    for col in columns:
        tree.heading(col, text=col)
        tree.column(col, width=120, anchor='center')

    # إضافة scrollbar
    scrollbar = ttk.Scrollbar(parent, orient="vertical", command=tree.yview)
    tree.configure(yscrollcommand=scrollbar.set)

    # تخطيط الجدول
    tree.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # تحميل البيانات
    load_inventory_data(tree)

    # ربط النقر المزدوج لتعديل المنتج
    tree.bind("<Double-1>", lambda event: edit_product(tree))

    return tree

def load_inventory_data(tree):
    """تحميل بيانات المخزون"""
    # مسح البيانات الموجودة
    for item in tree.get_children():
        tree.delete(item)

    try:
        db = connect_db()
        if db:
            cursor = db.cursor()
            cursor.execute("SELECT product_name, category, wholesale_price, selling_price, quantity, expiry_date FROM inventory")
            rows = cursor.fetchall()

            for row in rows:
                # تنسيق تاريخ الانتهاء
                expiry_date = row[5].strftime('%Y-%m-%d') if row[5] else 'غير محدد'
                tree.insert("", "end", values=(row[0], row[1], f"{row[2]:.2f}", f"{row[3]:.2f}", row[4], expiry_date))

            cursor.close()
            db.close()
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في تحميل بيانات المخزون: {str(e)}")

def search_products(search_term):
    """البحث في المنتجات"""
    if not search_term.strip():
        show_inventory()
        return

    clear_content()

    # عنوان البحث
    tk.Label(main_content, text=f"نتائج البحث عن: {search_term}",
             font=FONTS['title'], bg=COLORS['bg_main'], fg=COLORS['primary']).pack(pady=20)

    # زر العودة
    tk.Button(main_content, text="← العودة للمخزون", command=show_inventory,
              bg=COLORS['accent'], fg=COLORS['text_white'], font=FONTS['button']).pack(pady=10)

    # إطار الجدول
    table_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    table_frame.pack(fill="both", expand=True, padx=20, pady=10)

    # إنشاء الجدول
    from tkinter import ttk
    columns = ('اسم المنتج', 'الفئة', 'سعر الشراء', 'سعر البيع', 'الكمية', 'تاريخ الانتهاء')
    tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

    for col in columns:
        tree.heading(col, text=col)
        tree.column(col, width=120, anchor='center')

    scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
    tree.configure(yscrollcommand=scrollbar.set)

    tree.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # البحث في قاعدة البيانات
    try:
        db = connect_db()
        if db:
            cursor = db.cursor()
            search_query = f"%{search_term}%"
            cursor.execute("""SELECT product_name, category, wholesale_price, selling_price, quantity, expiry_date
                             FROM inventory WHERE product_name LIKE %s OR category LIKE %s""",
                          (search_query, search_query))
            rows = cursor.fetchall()

            for row in rows:
                expiry_date = row[5].strftime('%Y-%m-%d') if row[5] else 'غير محدد'
                tree.insert("", "end", values=(row[0], row[1], f"{row[2]:.2f}", f"{row[3]:.2f}", row[4], expiry_date))

            cursor.close()
            db.close()

            if not rows:
                tk.Label(main_content, text="لم يتم العثور على نتائج",
                        font=FONTS['main'], bg=COLORS['bg_main'], fg=COLORS['error']).pack(pady=20)
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")

def add_product_window():
    """نافذة إضافة منتج جديد"""
    add_window = tk.Toplevel(root)
    add_window.title("إضافة منتج جديد - صيدلية الشفاء")
    add_window.geometry("550x650")
    add_window.configure(bg=COLORS['bg_card'])
    add_window.resizable(False, False)

    # توسيط النافذة
    add_window.transient(root)
    add_window.grab_set()

    # توسيط النافذة على الشاشة
    add_window.update_idletasks()
    width = add_window.winfo_width()
    height = add_window.winfo_height()
    x = (add_window.winfo_screenwidth() // 2) - (width // 2)
    y = (add_window.winfo_screenheight() // 2) - (height // 2)
    add_window.geometry(f'{width}x{height}+{x}+{y}')

    # العنوان
    tk.Label(add_window, text="إضافة منتج جديد", font=FONTS['title'],
             bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=20)

    # إطار الحقول
    fields_frame = tk.Frame(add_window, bg=COLORS['bg_card'])
    fields_frame.pack(padx=30, pady=20, fill="both", expand=True)

    # الحقول
    fields = [
        ("اسم المنتج:", "product_name"),
        ("الفئة:", "category"),
        ("سعر الشراء:", "wholesale_price"),
        ("سعر البيع:", "selling_price"),
        ("الكمية:", "quantity"),
        ("تاريخ الانتهاء (YYYY-MM-DD):", "expiry_date")
    ]

    entries = {}
    for label_text, field_name in fields:
        tk.Label(fields_frame, text=label_text, font=FONTS['main'],
                bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", pady=(10, 5))

        entry = tk.Entry(fields_frame, font=FONTS['main'], width=30, relief="solid", bd=1)
        entry.pack(fill="x", pady=(0, 10))
        entries[field_name] = entry

    # إضافة قيم افتراضية مفيدة
    entries['category'].insert(0, "دواء")
    entries['wholesale_price'].insert(0, "0.00")
    entries['selling_price'].insert(0, "0.00")
    entries['quantity'].insert(0, "1")
    entries['expiry_date'].insert(0, "2025-12-31")

    # تركيز على حقل اسم المنتج
    entries['product_name'].focus()

    # مساحة فارغة
    tk.Label(add_window, text="", bg=COLORS['bg_card'], height=1).pack()

    # إطار الأزرار بسيط وواضح
    buttons_frame = tk.Frame(add_window, bg=COLORS['bg_card'])
    buttons_frame.pack(pady=30, padx=30, fill="x")

    # زر إضافة المنتج
    add_btn = tk.Button(buttons_frame,
                       text="إضافة المنتج",
                       command=lambda: save_new_product(entries, add_window),
                       bg="#28a745",
                       fg="white",
                       font=("Arial", 14, "bold"),
                       width=20,
                       height=2,
                       relief="raised",
                       bd=3)
    add_btn.pack(side="left", padx=10, expand=True, fill="x")

    # زر الإلغاء
    cancel_btn = tk.Button(buttons_frame,
                          text="إلغاء",
                          command=add_window.destroy,
                          bg="#dc3545",
                          fg="white",
                          font=("Arial", 14, "bold"),
                          width=20,
                          height=2,
                          relief="raised",
                          bd=3)
    cancel_btn.pack(side="right", padx=10, expand=True, fill="x")

    # ربط مفتاح Enter لحفظ المنتج
    add_window.bind('<Return>', lambda event: save_new_product(entries, add_window))
    add_window.bind('<Escape>', lambda event: add_window.destroy())

    # تأكيد إنشاء الأزرار
    print(" تم إنشاء أزرار إضافة المنتج بنجاح")

    # إجبار تحديث النافذة
    add_window.update_idletasks()
    add_window.update()

def save_new_product(entries, window):
    """حفظ منتج جديد"""
    try:
        # التحقق من البيانات
        product_name = entries['product_name'].get().strip()
        category = entries['category'].get().strip()
        wholesale_price = entries['wholesale_price'].get().strip()
        selling_price = entries['selling_price'].get().strip()
        quantity = entries['quantity'].get().strip()
        expiry_date = entries['expiry_date'].get().strip()

        if not all([product_name, category, wholesale_price, selling_price, quantity]):
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
            return

        # التحقق من الأرقام
        try:
            wholesale_price = float(wholesale_price)
            selling_price = float(selling_price)
            quantity = int(quantity)

            # التحقق من القيم المنطقية
            if wholesale_price < 0 or selling_price < 0 or quantity < 0:
                messagebox.showerror("خطأ", "لا يمكن أن تكون الأسعار أو الكمية أقل من صفر")
                return

            if selling_price < wholesale_price:
                result = messagebox.askyesno("تحذير", "سعر البيع أقل من سعر الشراء. هل تريد المتابعة؟")
                if not result:
                    return

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة للأسعار والكمية")
            return

        # حفظ في قاعدة البيانات
        db = connect_db()
        if db:
            cursor = db.cursor()

            # التحقق من عدم وجود منتج بنفس الاسم
            cursor.execute("SELECT id FROM inventory WHERE product_name = %s", (product_name,))
            if cursor.fetchone():
                messagebox.showerror("خطأ", "يوجد منتج بنفس الاسم مسبقاً")
                cursor.close()
                db.close()
                return

            # إدراج المنتج الجديد
            query = """INSERT INTO inventory (product_name, category, wholesale_price, selling_price, quantity, expiry_date)
                      VALUES (%s, %s, %s, %s, %s, %s)"""

            expiry_date_value = expiry_date if expiry_date else None
            cursor.execute(query, (product_name, category, wholesale_price, selling_price, quantity, expiry_date_value))

            db.commit()
            cursor.close()
            db.close()

            messagebox.showinfo(" تم بنجاح", f"تم إضافة المنتج '{product_name}' بنجاح!\n\nالكمية: {quantity}\nسعر البيع: {selling_price} أوقية")
            window.destroy()
            show_inventory()  # تحديث المخزون

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في إضافة المنتج: {str(e)}")

def edit_product(tree):
    """تعديل منتج محدد"""
    selected = tree.selection()
    if not selected:
        messagebox.showwarning("تحذير", "يرجى اختيار منتج للتعديل")
        return

    # الحصول على بيانات المنتج المحدد
    item = tree.item(selected[0])
    values = item['values']

    # إنشاء نافذة التعديل
    edit_window = tk.Toplevel(root)
    edit_window.title("تعديل المنتج")
    edit_window.geometry("400x500")
    edit_window.configure(bg=COLORS['bg_card'])
    edit_window.resizable(False, False)

    edit_window.transient(root)
    edit_window.grab_set()

    # العنوان
    tk.Label(edit_window, text="تعديل المنتج", font=FONTS['title'],
             bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=20)

    # إطار الحقول
    fields_frame = tk.Frame(edit_window, bg=COLORS['bg_card'])
    fields_frame.pack(padx=30, pady=20, fill="both", expand=True)

    # الحقول مع القيم الحالية
    fields = [
        ("اسم المنتج:", "product_name", values[0]),
        ("الفئة:", "category", values[1]),
        ("سعر الشراء:", "wholesale_price", values[2]),
        ("سعر البيع:", "selling_price", values[3]),
        ("الكمية:", "quantity", values[4]),
        ("تاريخ الانتهاء (YYYY-MM-DD):", "expiry_date", values[5])
    ]

    entries = {}
    for label_text, field_name, current_value in fields:
        tk.Label(fields_frame, text=label_text, font=FONTS['main'],
                bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", pady=(10, 5))

        entry = tk.Entry(fields_frame, font=FONTS['main'], width=30, relief="solid", bd=1)
        entry.pack(fill="x", pady=(0, 10))
        entry.insert(0, str(current_value))
        entries[field_name] = entry

    # إطار الأزرار
    buttons_frame = tk.Frame(edit_window, bg=COLORS['bg_card'])
    buttons_frame.pack(pady=20)

    # زر الحفظ
    save_btn = tk.Button(buttons_frame, text="حفظ التغييرات",
                        command=lambda: update_product(entries, values[0], edit_window),
                        bg=COLORS['btn_success'], fg=COLORS['text_white'],
                        font=FONTS['button'], width=15)
    save_btn.pack(side="left", padx=10)

    # زر الحذف
    delete_btn = tk.Button(buttons_frame, text="حذف المنتج",
                          command=lambda: delete_product(values[0], edit_window),
                          bg=COLORS['error'], fg=COLORS['text_white'],
                          font=FONTS['button'], width=15)
    delete_btn.pack(side="left", padx=10)

    # زر الإلغاء
    cancel_btn = tk.Button(buttons_frame, text="إلغاء", command=edit_window.destroy,
                          bg=COLORS['accent'], fg=COLORS['text_white'],
                          font=FONTS['button'], width=15)
    cancel_btn.pack(side="left", padx=10)

def update_product(entries, original_name, window):
    """تحديث بيانات المنتج"""
    try:
        # الحصول على البيانات الجديدة
        product_name = entries['product_name'].get().strip()
        category = entries['category'].get().strip()
        wholesale_price = entries['wholesale_price'].get().strip()
        selling_price = entries['selling_price'].get().strip()
        quantity = entries['quantity'].get().strip()
        expiry_date = entries['expiry_date'].get().strip()

        if not all([product_name, category, wholesale_price, selling_price, quantity]):
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
            return

        # التحقق من الأرقام
        try:
            wholesale_price = float(wholesale_price)
            selling_price = float(selling_price)
            quantity = int(quantity)
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة للأسعار والكمية")
            return

        # تحديث في قاعدة البيانات
        db = connect_db()
        if db:
            cursor = db.cursor()

            query = """UPDATE inventory SET product_name = %s, category = %s, wholesale_price = %s,
                      selling_price = %s, quantity = %s, expiry_date = %s WHERE product_name = %s"""

            expiry_date_value = expiry_date if expiry_date and expiry_date != 'غير محدد' else None
            cursor.execute(query, (product_name, category, wholesale_price, selling_price,
                                 quantity, expiry_date_value, original_name))

            db.commit()
            cursor.close()
            db.close()

            messagebox.showinfo(f"{ICONS['success']} تم تحديث المنتج",
                               f"✅ تم تحديث المنتج بنجاح!\n"
                               f"📦 المنتج محدث في النظام\n"
                               f"💾 البيانات محفوظة")
            window.destroy()
            show_inventory()  # تحديث المخزون

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في تحديث المنتج: {str(e)}")

def delete_product(product_name, window):
    """حذف منتج"""
    result = messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف المنتج '{product_name}' نهائياً؟")
    if result:
        try:
            db = connect_db()
            if db:
                cursor = db.cursor()
                cursor.execute("DELETE FROM inventory WHERE product_name = %s", (product_name,))
                db.commit()
                cursor.close()
                db.close()

                messagebox.showinfo(f"{ICONS['success']} تم حذف المنتج",
                                   f"✅ تم حذف المنتج بنجاح!\n"
                                   f"🗑️ المنتج محذوف من النظام\n"
                                   f"📊 المخزون محدث")
                window.destroy()
                show_inventory()  # تحديث المخزون

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف المنتج: {str(e)}")

def show_sales():
    """عرض نظام المبيعات الكامل"""
    clear_content()

    # عنوان الصفحة
    title_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    title_frame.pack(fill="x", pady=10)

    tk.Label(title_frame, text="نظام المبيعات", font=FONTS['title'],
             bg=COLORS['bg_main'], fg=COLORS['primary']).pack(side="left", padx=20)

    # زر عرض تقرير المبيعات
    report_btn = tk.Button(title_frame, text="📊 تقرير المبيعات", command=show_sales_report,
                          bg=COLORS['accent'], fg=COLORS['text_white'], font=FONTS['button'])
    report_btn.pack(side="right", padx=20)

    # إطار رئيسي مقسم مع تحسين الحجم
    main_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    main_frame.pack(fill="both", expand=True, padx=10, pady=5)

    # الجانب الأيسر - اختيار المنتجات (أصغر)
    left_frame = tk.Frame(main_frame, bg=COLORS['bg_card'], relief="raised", bd=2)
    left_frame.pack(side="left", fill="both", expand=True, padx=(0, 5))

    # الجانب الأيمن - السلة والدفع (أكبر)
    right_frame = tk.Frame(main_frame, bg=COLORS['bg_card'], relief="raised", bd=2)
    right_frame.pack(side="right", fill="both", expand=True, padx=(5, 0))

    # إنشاء واجهة اختيار المنتجات
    create_product_selection(left_frame)

    # إنشاء واجهة السلة والدفع
    create_cart_and_payment(right_frame)

def create_product_selection(parent):
    """إنشاء واجهة اختيار المنتجات"""
    # عنوان القسم
    tk.Label(parent, text="اختيار المنتجات", font=FONTS['heading'],
             bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=10)

    # إطار البحث
    search_frame = tk.Frame(parent, bg=COLORS['bg_card'])
    search_frame.pack(fill="x", padx=10, pady=5)

    tk.Label(search_frame, text="البحث:", font=FONTS['main'], bg=COLORS['bg_card']).pack(side="left")
    global product_search_entry
    product_search_entry = tk.Entry(search_frame, font=FONTS['main'], width=20)
    product_search_entry.pack(side="left", padx=5)

    search_btn = tk.Button(search_frame, text="بحث", command=filter_products,
                          bg=COLORS['accent'], fg=COLORS['text_white'], font=FONTS['button'])
    search_btn.pack(side="left", padx=5)

    # قائمة المنتجات
    products_frame = tk.Frame(parent, bg=COLORS['bg_card'])
    products_frame.pack(fill="both", expand=True, padx=10, pady=10)

    # إنشاء جدول المنتجات (أصغر)
    from tkinter import ttk
    global products_tree
    columns = ('المنتج', 'السعر', 'المتاح')
    products_tree = ttk.Treeview(products_frame, columns=columns, show='headings', height=8)

    for col in columns:
        products_tree.heading(col, text=col)
        products_tree.column(col, width=100, anchor='center')

    # scrollbar للمنتجات
    products_scrollbar = ttk.Scrollbar(products_frame, orient="vertical", command=products_tree.yview)
    products_tree.configure(yscrollcommand=products_scrollbar.set)

    products_tree.pack(side="left", fill="both", expand=True)
    products_scrollbar.pack(side="right", fill="y")

    # تحميل المنتجات
    load_products_for_sale()

    # إطار إضافة للسلة
    add_frame = tk.Frame(parent, bg=COLORS['bg_card'])
    add_frame.pack(fill="x", padx=10, pady=10)

    tk.Label(add_frame, text="الكمية:", font=FONTS['main'], bg=COLORS['bg_card']).pack(side="left")
    global quantity_entry
    quantity_entry = tk.Entry(add_frame, font=FONTS['main'], width=10)
    quantity_entry.pack(side="left", padx=5)
    quantity_entry.insert(0, "1")

    add_to_cart_btn = tk.Button(add_frame, text="إضافة للسلة", command=add_to_cart,
                               bg=COLORS['btn_success'], fg=COLORS['text_white'], font=FONTS['button'])
    add_to_cart_btn.pack(side="left", padx=10)

def create_cart_and_payment(parent):
    """إنشاء واجهة السلة والدفع"""
    # عنوان القسم
    tk.Label(parent, text="السلة والدفع", font=FONTS['heading'],
             bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=10)

    # السلة
    cart_frame = tk.Frame(parent, bg=COLORS['bg_card'])
    cart_frame.pack(fill="both", expand=True, padx=10, pady=5)

    from tkinter import ttk
    global cart_tree
    cart_columns = ('المنتج', 'السعر', 'الكمية', 'الإجمالي')
    cart_tree = ttk.Treeview(cart_frame, columns=cart_columns, show='headings', height=5)

    for col in cart_columns:
        cart_tree.heading(col, text=col)
        cart_tree.column(col, width=80, anchor='center')

    cart_scrollbar = ttk.Scrollbar(cart_frame, orient="vertical", command=cart_tree.yview)
    cart_tree.configure(yscrollcommand=cart_scrollbar.set)

    cart_tree.pack(side="left", fill="both", expand=True)
    cart_scrollbar.pack(side="right", fill="y")

    # أزرار السلة
    cart_buttons_frame = tk.Frame(parent, bg=COLORS['bg_card'])
    cart_buttons_frame.pack(fill="x", padx=10, pady=5)

    remove_btn = tk.Button(cart_buttons_frame, text="حذف من السلة", command=remove_from_cart,
                          bg=COLORS['error'], fg=COLORS['text_white'], font=FONTS['button'])
    remove_btn.pack(side="left", padx=5)

    clear_btn = tk.Button(cart_buttons_frame, text="مسح السلة", command=clear_cart,
                         bg=COLORS['accent'], fg=COLORS['text_white'], font=FONTS['button'])
    clear_btn.pack(side="left", padx=5)

    # الإجمالي
    total_frame = tk.Frame(parent, bg=COLORS['bg_card'])
    total_frame.pack(fill="x", padx=10, pady=10)

    global total_label
    total_label = tk.Label(total_frame, text="الإجمالي: 0.00", font=FONTS['heading'],
                          bg=COLORS['bg_card'], fg=COLORS['primary'])
    total_label.pack()

    # زر إتمام البيع والطباعة المباشرة
    complete_sale_btn = tk.Button(total_frame,
                                 text="اتمام البيع وانشاء فاتورة PDF",
                                 command=complete_sale_with_print,
                                 bg="#28a745",
                                 fg="white",
                                 font=("Arial", 12, "bold"),
                                 width=35,
                                 height=2,
                                 relief="raised",
                                 bd=3)
    complete_sale_btn.pack(pady=10)

    # معلومات العميل
    customer_frame = tk.Frame(parent, bg=COLORS['bg_card'])
    customer_frame.pack(fill="x", padx=10, pady=5)

    tk.Label(customer_frame, text="اسم العميل:", font=FONTS['main'], bg=COLORS['bg_card']).pack(anchor="w")
    global customer_entry
    customer_entry = tk.Entry(customer_frame, font=FONTS['main'], width=25)
    customer_entry.pack(fill="x", pady=2)

    tk.Label(customer_frame, text="رقم الهاتف:", font=FONTS['main'], bg=COLORS['bg_card']).pack(anchor="w")
    global phone_entry
    phone_entry = tk.Entry(customer_frame, font=FONTS['main'], width=25)
    phone_entry.pack(fill="x", pady=2)

    # طريقة الدفع (مضغوطة)
    payment_frame = tk.Frame(parent, bg=COLORS['bg_card'])
    payment_frame.pack(fill="x", padx=10, pady=3)

    tk.Label(payment_frame, text="طريقة الدفع:", font=("Arial", 10, "bold"),
             bg=COLORS['bg_card']).pack(anchor="w")

    global payment_var
    payment_var = tk.StringVar(value="نقد")

    # تقسيم خيارات الدفع في صفين
    payment_methods = ["نقد", "بنكلي", "مصرفي", "سداد", "BIC Bank", "Click"]

    # الصف الأول
    row1_frame = tk.Frame(payment_frame, bg=COLORS['bg_card'])
    row1_frame.pack(fill="x", pady=2)

    for method in payment_methods[:3]:
        tk.Radiobutton(row1_frame, text=method, variable=payment_var, value=method,
                      bg=COLORS['bg_card'], font=("Arial", 9)).pack(side="left", padx=5)

    # الصف الثاني
    row2_frame = tk.Frame(payment_frame, bg=COLORS['bg_card'])
    row2_frame.pack(fill="x", pady=2)

    for method in payment_methods[3:]:
        tk.Radiobutton(row2_frame, text=method, variable=payment_var, value=method,
                      bg=COLORS['bg_card'], font=("Arial", 9)).pack(side="left", padx=5)

    # تأكيد إنشاء زر إتمام البيع
    print(" تم إنشاء زر إتمام البيع والطباعة المباشرة بنجاح")

# متغيرات السلة والفاتورة
cart_items = []
last_invoice_data = None  # لحفظ بيانات آخر فاتورة

def load_products_for_sale():
    """تحميل المنتجات المتاحة للبيع"""
    try:
        # مسح البيانات الموجودة
        for item in products_tree.get_children():
            products_tree.delete(item)

        db = connect_db()
        if db:
            cursor = db.cursor()
            cursor.execute("SELECT product_name, selling_price, quantity FROM inventory WHERE quantity > 0")
            rows = cursor.fetchall()

            for row in rows:
                products_tree.insert("", "end", values=(row[0], f"{row[1]:.2f}", row[2]))

            cursor.close()
            db.close()
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في تحميل المنتجات: {str(e)}")

def filter_products():
    """فلترة المنتجات حسب البحث"""
    search_term = product_search_entry.get().strip()

    try:
        # مسح البيانات الموجودة
        for item in products_tree.get_children():
            products_tree.delete(item)

        db = connect_db()
        if db:
            cursor = db.cursor()
            if search_term:
                search_query = f"%{search_term}%"
                cursor.execute("SELECT product_name, selling_price, quantity FROM inventory WHERE quantity > 0 AND product_name LIKE %s", (search_query,))
            else:
                cursor.execute("SELECT product_name, selling_price, quantity FROM inventory WHERE quantity > 0")

            rows = cursor.fetchall()

            for row in rows:
                products_tree.insert("", "end", values=(row[0], f"{row[1]:.2f}", row[2]))

            cursor.close()
            db.close()
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")

def add_to_cart():
    """إضافة منتج للسلة"""
    selected = products_tree.selection()
    if not selected:
        messagebox.showwarning("تحذير", "يرجى اختيار منتج")
        return

    try:
        quantity = int(quantity_entry.get().strip())
        if quantity <= 0:
            messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة")
            return
    except ValueError:
        messagebox.showerror("خطأ", "يرجى إدخال رقم صحيح للكمية")
        return

    # الحصول على بيانات المنتج
    item = products_tree.item(selected[0])
    values = item['values']
    product_name = values[0]
    price = float(values[1])
    available = int(values[2])

    if quantity > available:
        messagebox.showerror("خطأ", f"الكمية المطلوبة ({quantity}) أكبر من المتاح ({available})")
        return

    # التحقق من وجود المنتج في السلة
    for i, cart_item in enumerate(cart_items):
        if cart_item[0] == product_name:
            # تحديث الكمية
            new_quantity = cart_item[2] + quantity
            if new_quantity > available:
                messagebox.showerror("خطأ", f"إجمالي الكمية ({new_quantity}) أكبر من المتاح ({available})")
                return
            cart_items[i] = (product_name, price, new_quantity, price * new_quantity)
            break
    else:
        # إضافة منتج جديد للسلة
        total = price * quantity
        cart_items.append((product_name, price, quantity, total))

    # تحديث عرض السلة
    update_cart_display()

    # مسح حقل الكمية
    quantity_entry.delete(0, tk.END)
    quantity_entry.insert(0, "1")

def update_cart_display():
    """تحديث عرض السلة"""
    # مسح السلة الحالية
    for item in cart_tree.get_children():
        cart_tree.delete(item)

    # إضافة العناصر
    total_amount = 0
    for item in cart_items:
        cart_tree.insert("", "end", values=(item[0], f"{item[1]:.2f}", item[2], f"{item[3]:.2f}"))
        total_amount += item[3]

    # تحديث الإجمالي
    total_label.config(text=f"الإجمالي: {total_amount:.2f}")

def remove_from_cart():
    """حذف منتج من السلة"""
    selected = cart_tree.selection()
    if not selected:
        messagebox.showwarning("تحذير", "يرجى اختيار منتج من السلة")
        return

    # الحصول على اسم المنتج
    item = cart_tree.item(selected[0])
    product_name = item['values'][0]

    # حذف من قائمة السلة
    cart_items[:] = [item for item in cart_items if item[0] != product_name]

    # تحديث العرض
    update_cart_display()

def clear_cart():
    """مسح السلة بالكامل"""
    result = messagebox.askyesno("تأكيد", "هل تريد مسح جميع عناصر السلة؟")
    if result:
        cart_items.clear()
        update_cart_display()

def complete_sale():
    """إتمام البيع"""
    if not cart_items:
        messagebox.showwarning("تحذير", "السلة فارغة")
        return

    customer_name = customer_entry.get().strip()
    phone = phone_entry.get().strip()
    payment_method = payment_var.get()

    if not customer_name:
        customer_name = "عميل"

    try:
        db = connect_db()
        if db:
            cursor = db.cursor()

            # إنشاء رقم فاتورة
            from datetime import datetime
            now = datetime.now()
            invoice_id = f"INV-{now.strftime('%Y%m%d%H%M%S')}"

            # حفظ المبيعات وتحديث المخزون
            for item in cart_items:
                product_name, price, quantity, total = item

                # إدراج في جدول المبيعات
                cursor.execute("""INSERT INTO sales (invoice_id, product_name, price, qty, total, time, payment_method, customer, phone, date)
                                 VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                              (invoice_id, product_name, price, quantity, total, now.strftime("%H:%M:%S"),
                               payment_method, customer_name, phone, now.strftime("%Y-%m-%d")))

                # تحديث المخزون
                cursor.execute("UPDATE inventory SET quantity = quantity - %s WHERE product_name = %s",
                              (quantity, product_name))

            db.commit()
            cursor.close()
            db.close()

            # عرض رسالة نجاح مفصلة
            total_amount = sum(item[3] for item in cart_items)
            success_message = f""" تم إتمام البيع بنجاح!

 رقم الفاتورة: {invoice_id}
 العميل: {customer_name}
 الهاتف: {phone if phone else 'غير محدد'}
 طريقة الدفع: {payment_method}
 الإجمالي: {total_amount:.2f} أوقية

عدد المنتجات: {len(cart_items)}"""

            messagebox.showinfo(" تم بنجاح", success_message)

            # طباعة الفاتورة
            print_invoice(invoice_id, cart_items, customer_name, phone, payment_method, total_amount)

            # مسح السلة وإعادة تعيين الحقول
            cart_items.clear()
            update_cart_display()
            customer_entry.delete(0, tk.END)
            phone_entry.delete(0, tk.END)
            payment_var.set("نقد")

            # تحديث قائمة المنتجات لإظهار الكميات الجديدة
            load_products_for_sale()
            customer_entry.delete(0, tk.END)
            phone_entry.delete(0, tk.END)
            load_products_for_sale()  # تحديث المنتجات المتاحة

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في إتمام البيع: {str(e)}")

def print_invoice(invoice_id, items, customer, phone, payment_method, total):
    """طباعة الفاتورة (محاكاة)"""
    print("\n" + "="*50)
    print("           صيدلية الشفاء")
    print("         فاتورة مبيعات")
    print("="*50)
    print(f"رقم الفاتورة: {invoice_id}")
    print(f"العميل: {customer}")
    print(f"الهاتف: {phone}")
    print(f"طريقة الدفع: {payment_method}")
    print(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-"*50)
    print(f"{'المنتج':<20} {'السعر':<10} {'الكمية':<8} {'الإجمالي':<10}")
    print("-"*50)

    for item in items:
        print(f"{item[0]:<20} {item[1]:<10.2f} {item[2]:<8} {item[3]:<10.2f}")

    print("-"*50)
    print(f"{'الإجمالي النهائي:':<39} {total:<10.2f}")
    print("="*50)
    print("شكراً لتعاملكم معنا")
    print("="*50)

def complete_sale_with_print():
    """إتمام البيع مع الطباعة المباشرة"""
    if not cart_items:
        messagebox.showwarning("تحذير", "السلة فارغة")
        return

    customer_name = customer_entry.get().strip()
    phone = phone_entry.get().strip()
    payment_method = payment_var.get()

    if not customer_name:
        customer_name = "عميل"

    try:
        db = connect_db()
        if db:
            cursor = db.cursor()

            # إنشاء رقم فاتورة
            from datetime import datetime
            now = datetime.now()
            invoice_id = f"INV-{now.strftime('%Y%m%d%H%M%S')}"

            # حساب الإجمالي
            total_amount = sum(item[3] for item in cart_items)

            # حفظ المبيعات وتحديث المخزون
            for item in cart_items:
                product_name, price, quantity, total = item

                # إدراج في جدول المبيعات
                cursor.execute("""INSERT INTO sales (invoice_id, product_name, price, qty, total, time, payment_method, customer, phone, date)
                                 VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                              (invoice_id, product_name, price, quantity, total, now.strftime("%H:%M:%S"),
                               payment_method, customer_name, phone, now.strftime("%Y-%m-%d")))

                # تحديث المخزون
                cursor.execute("UPDATE inventory SET quantity = quantity - %s WHERE product_name = %s",
                              (quantity, product_name))

            db.commit()
            cursor.close()
            db.close()

            # إنشاء فاتورة PDF
            invoice_data = {
                'invoice_id': invoice_id,
                'items': cart_items.copy(),
                'customer': customer_name,
                'phone': phone,
                'payment_method': payment_method,
                'total': total_amount,
                'date': now.strftime("%Y-%m-%d"),
                'time': now.strftime("%H:%M:%S")
            }

            # إنشاء PDF أولاً، ثم الطباعة النصية كنسخة احتياطية
            pdf_created = create_pdf_invoice(invoice_data)
            if not pdf_created:
                print_invoice_detailed(invoice_data)

            # عرض رسالة نجاح
            success_message = f"""تم إتمام البيع وإنشاء الفاتورة بنجاح!

رقم الفاتورة: {invoice_id}
العميل: {customer_name}
الهاتف: {phone if phone else 'غير محدد'}
طريقة الدفع: {payment_method}
الإجمالي: {total_amount:.2f} أوقية

عدد المنتجات: {len(cart_items)}

تم إنشاء فاتورة PDF وفتحها تلقائياً"""

            messagebox.showinfo(" تم بنجاح", success_message)

            # مسح السلة وإعادة تعيين الحقول
            cart_items.clear()
            update_cart_display()
            customer_entry.delete(0, tk.END)
            phone_entry.delete(0, tk.END)
            payment_var.set("نقد")

            # تحديث قائمة المنتجات لإظهار الكميات الجديدة
            load_products_for_sale()

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في إتمام البيع: {str(e)}")

def complete_sale_and_enable_print():
    """إتمام البيع وتفعيل زر الطباعة"""
    global last_invoice_data

    if not cart_items:
        messagebox.showwarning("تحذير", "السلة فارغة")
        return

    customer_name = customer_entry.get().strip()
    phone = phone_entry.get().strip()
    payment_method = payment_var.get()

    if not customer_name:
        customer_name = "عميل"

    try:
        db = connect_db()
        if db:
            cursor = db.cursor()

            # إنشاء رقم فاتورة
            from datetime import datetime
            now = datetime.now()
            invoice_id = f"INV-{now.strftime('%Y%m%d%H%M%S')}"

            # حفظ بيانات الفاتورة للطباعة
            total_amount = sum(item[3] for item in cart_items)
            last_invoice_data = {
                'invoice_id': invoice_id,
                'items': cart_items.copy(),
                'customer': customer_name,
                'phone': phone,
                'payment_method': payment_method,
                'total': total_amount,
                'date': now.strftime("%Y-%m-%d"),
                'time': now.strftime("%H:%M:%S")
            }

            # حفظ المبيعات وتحديث المخزون
            for item in cart_items:
                product_name, price, quantity, total = item

                # إدراج في جدول المبيعات
                cursor.execute("""INSERT INTO sales (invoice_id, product_name, price, qty, total, time, payment_method, customer, phone, date)
                                 VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                              (invoice_id, product_name, price, quantity, total, now.strftime("%H:%M:%S"),
                               payment_method, customer_name, phone, now.strftime("%Y-%m-%d")))

                # تحديث المخزون
                cursor.execute("UPDATE inventory SET quantity = quantity - %s WHERE product_name = %s",
                              (quantity, product_name))

            db.commit()
            cursor.close()
            db.close()

            # عرض رسالة نجاح
            success_message = f""" تم إتمام البيع بنجاح!

 رقم الفاتورة: {invoice_id}
 العميل: {customer_name}
 الهاتف: {phone if phone else 'غير محدد'}
 طريقة الدفع: {payment_method}
 الإجمالي: {total_amount:.2f} أوقية

عدد المنتجات: {len(cart_items)}

✨ يمكنك الآن طباعة الفاتورة من الزر الأزرق"""

            messagebox.showinfo(" تم بنجاح", success_message)

            # لا حاجة لتفعيل أزرار إضافية - الطباعة تمت مباشرة

            # مسح السلة وإعادة تعيين الحقول
            cart_items.clear()
            update_cart_display()
            customer_entry.delete(0, tk.END)
            phone_entry.delete(0, tk.END)
            payment_var.set("نقد")

            # تحديث قائمة المنتجات لإظهار الكميات الجديدة
            load_products_for_sale()

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في إتمام البيع: {str(e)}")



def create_pdf_invoice(invoice_data):
    """إنشاء فاتورة PDF"""
    if not PDF_AVAILABLE:
        print("مكتبة PDF غير متوفرة، سيتم استخدام الطباعة النصية")
        return print_invoice_detailed(invoice_data)

    try:
        # إنشاء اسم الملف
        invoice_id = invoice_data['invoice_id']
        filename = f"invoice_{invoice_id}.pdf"

        # إنشاء ملف PDF
        c = canvas.Canvas(filename, pagesize=A4)
        width, height = A4

        # إعداد الخط (استخدام خط افتراضي يدعم العربية)
        c.setFont("Helvetica-Bold", 16)

        # العنوان الرئيسي
        c.drawCentredString(width/2, height - 80, "Shifa Pharmacy")
        c.setFont("Helvetica", 14)
        c.drawCentredString(width/2, height - 100, "Sales Invoice")

        # خط فاصل
        c.line(50, height - 120, width - 50, height - 120)

        # معلومات الفاتورة
        y_position = height - 150
        c.setFont("Helvetica", 12)

        # معلومات أساسية
        c.drawString(50, y_position, f"Invoice ID: {invoice_data['invoice_id']}")
        c.drawString(300, y_position, f"Date: {invoice_data['date']}")

        y_position -= 20
        c.drawString(50, y_position, f"Time: {invoice_data['time']}")
        c.drawString(300, y_position, f"Customer: {invoice_data['customer']}")

        y_position -= 20
        c.drawString(50, y_position, f"Phone: {invoice_data['phone'] if invoice_data['phone'] else 'N/A'}")
        c.drawString(300, y_position, f"Payment: {invoice_data['payment_method']}")

        # خط فاصل
        y_position -= 30
        c.line(50, y_position, width - 50, y_position)

        # عناوين الجدول
        y_position -= 30
        c.setFont("Helvetica-Bold", 11)
        c.drawString(50, y_position, "Product")
        c.drawString(200, y_position, "Price")
        c.drawString(280, y_position, "Qty")
        c.drawString(350, y_position, "Total")

        # خط تحت العناوين
        y_position -= 10
        c.line(50, y_position, width - 50, y_position)

        # المنتجات
        c.setFont("Helvetica", 10)
        y_position -= 20

        for item in invoice_data['items']:
            product_name, price, quantity, total = item
            c.drawString(50, y_position, str(product_name)[:25])
            c.drawString(200, y_position, f"{price:.2f}")
            c.drawString(280, y_position, str(quantity))
            c.drawString(350, y_position, f"{total:.2f}")
            y_position -= 20

        # خط فاصل قبل الإجمالي
        y_position -= 10
        c.line(50, y_position, width - 50, y_position)

        # الإجمالي النهائي
        y_position -= 30
        c.setFont("Helvetica-Bold", 14)
        c.drawString(250, y_position, f"Total: {invoice_data['total']:.2f} MRU")

        # رسالة الشكر
        y_position -= 60
        c.setFont("Helvetica", 12)
        c.drawCentredString(width/2, y_position, "Thank you for your business!")
        c.drawCentredString(width/2, y_position - 20, "Your health is our priority")

        # حفظ الملف
        c.save()

        # فتح الملف
        if platform.system() == "Windows":
            os.startfile(filename)
        elif platform.system() == "Darwin":  # macOS
            subprocess.call(["open", filename])
        else:  # Linux
            subprocess.call(["xdg-open", filename])

        messagebox.showinfo("تم إنشاء PDF", f"تم إنشاء الفاتورة بصيغة PDF:\n{filename}")
        return filename

    except Exception as e:
        print(f"خطأ في إنشاء PDF: {e}")
        messagebox.showerror("خطأ PDF", f"فشل في إنشاء PDF: {str(e)}")
        return print_invoice_detailed(invoice_data)

def print_invoice_detailed(invoice_data):
    """طباعة فاتورة مفصلة"""
    print("\n" + "="*60)
    print("                     صيدلية الشفاء")
    print("                    فاتورة مبيعات")
    print("="*60)
    print(f" رقم الفاتورة: {invoice_data['invoice_id']}")
    print(f"📅 التاريخ: {invoice_data['date']}")
    print(f"🕐 الوقت: {invoice_data['time']}")
    print(f" العميل: {invoice_data['customer']}")
    print(f" الهاتف: {invoice_data['phone'] if invoice_data['phone'] else 'غير محدد'}")
    print(f" طريقة الدفع: {invoice_data['payment_method']}")
    print("-"*60)
    print(f"{'المنتج':<25} {'السعر':<10} {'الكمية':<8} {'الإجمالي':<10}")
    print("-"*60)

    for item in invoice_data['items']:
        product_name, price, quantity, total = item
        print(f"{product_name:<25} {price:<10.2f} {quantity:<8} {total:<10.2f}")

    print("-"*60)
    print(f"{'الإجمالي النهائي:':<44} {invoice_data['total']:<10.2f} أوقية")
    print("="*60)
    print("                   شكراً لتعاملكم معنا")
    print("                    دواؤكم أمانة عندنا")
    print("="*60)

def show_sales_report():
    """عرض تقرير المبيعات"""
    clear_content()

    # عنوان التقرير
    tk.Label(main_content, text="تقرير المبيعات", font=FONTS['title'],
             bg=COLORS['bg_main'], fg=COLORS['primary']).pack(pady=20)

    # زر العودة
    tk.Button(main_content, text="← العودة للمبيعات", command=show_sales,
              bg=COLORS['accent'], fg=COLORS['text_white'], font=FONTS['button']).pack(pady=10)

    # إطار الإحصائيات
    stats_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    stats_frame.pack(pady=20)

    try:
        db = connect_db()
        if db:
            cursor = db.cursor()

            # إحصائيات اليوم
            cursor.execute("SELECT COUNT(DISTINCT invoice_id), SUM(total) FROM sales WHERE DATE(date) = CURDATE()")
            today_data = cursor.fetchone()
            today_invoices = today_data[0] or 0
            today_total = today_data[1] or 0

            # إحصائيات الشهر
            cursor.execute("SELECT COUNT(DISTINCT invoice_id), SUM(total) FROM sales WHERE MONTH(date) = MONTH(CURDATE()) AND YEAR(date) = YEAR(CURDATE())")
            month_data = cursor.fetchone()
            month_invoices = month_data[0] or 0
            month_total = month_data[1] or 0

            # إحصائيات إجمالية
            cursor.execute("SELECT COUNT(DISTINCT invoice_id), SUM(total) FROM sales")
            total_data = cursor.fetchone()
            total_invoices = total_data[0] or 0
            total_sales = total_data[1] or 0

            cursor.close()
            db.close()

            # عرض الإحصائيات
            tk.Label(stats_frame, text=f"مبيعات اليوم: {today_invoices} فاتورة - {today_total:.2f}",
                    font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=5)
            tk.Label(stats_frame, text=f"مبيعات الشهر: {month_invoices} فاتورة - {month_total:.2f}",
                    font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=5)
            tk.Label(stats_frame, text=f"إجمالي المبيعات: {total_invoices} فاتورة - {total_sales:.2f}",
                    font=FONTS['heading'], bg=COLORS['bg_main'], fg=COLORS['primary']).pack(pady=10)

    except Exception as e:
        tk.Label(stats_frame, text=f"خطأ في تحميل التقرير: {str(e)}",
                font=FONTS['main'], bg=COLORS['bg_main'], fg=COLORS['error']).pack(pady=10)

def show_invoices():
    """عرض إدارة الفواتير الكاملة"""
    clear_content()

    # عنوان الصفحة
    title_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    title_frame.pack(fill="x", pady=10)

    tk.Label(title_frame, text="إدارة الفواتير", font=FONTS['title'],
             bg=COLORS['bg_main'], fg=COLORS['primary']).pack(side="left", padx=20)

    # زر طباعة تقرير شامل
    print_report_btn = tk.Button(title_frame, text=" طباعة تقرير شامل", command=print_all_invoices_report,
                                bg=COLORS['accent'], fg=COLORS['text_white'], font=FONTS['button'])
    print_report_btn.pack(side="right", padx=20)

    # إطار البحث والفلترة
    search_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    search_frame.pack(fill="x", padx=20, pady=10)

    # البحث برقم الفاتورة
    tk.Label(search_frame, text="رقم الفاتورة:", font=FONTS['main'], bg=COLORS['bg_main']).pack(side="left")
    global invoice_search_entry
    invoice_search_entry = tk.Entry(search_frame, font=FONTS['main'], width=20)
    invoice_search_entry.pack(side="left", padx=5)

    # البحث بالعميل
    tk.Label(search_frame, text="العميل:", font=FONTS['main'], bg=COLORS['bg_main']).pack(side="left", padx=(20, 5))
    global customer_search_entry
    customer_search_entry = tk.Entry(search_frame, font=FONTS['main'], width=15)
    customer_search_entry.pack(side="left", padx=5)

    # البحث بالتاريخ
    tk.Label(search_frame, text="التاريخ:", font=FONTS['main'], bg=COLORS['bg_main']).pack(side="left", padx=(20, 5))
    global date_search_entry
    date_search_entry = tk.Entry(search_frame, font=FONTS['main'], width=12)
    date_search_entry.pack(side="left", padx=5)
    date_search_entry.insert(0, "YYYY-MM-DD")

    # أزرار البحث
    search_btn = tk.Button(search_frame, text="بحث", command=search_invoices,
                          bg=COLORS['accent'], fg=COLORS['text_white'], font=FONTS['button'])
    search_btn.pack(side="left", padx=10)

    refresh_btn = tk.Button(search_frame, text="عرض الكل", command=show_invoices,
                           bg=COLORS['primary'], fg=COLORS['text_white'], font=FONTS['button'])
    refresh_btn.pack(side="left", padx=5)

    # إطار الجدول
    table_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    table_frame.pack(fill="both", expand=True, padx=20, pady=10)

    # إنشاء جدول الفواتير
    create_invoices_table(table_frame)

def create_invoices_table(parent):
    """إنشاء جدول الفواتير"""
    from tkinter import ttk

    # إنشاء Treeview
    columns = ('رقم الفاتورة', 'العميل', 'الهاتف', 'الإجمالي', 'طريقة الدفع', 'التاريخ', 'الوقت')
    global invoices_tree
    invoices_tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)

    # تعريف العناوين والعرض
    column_widths = {'رقم الفاتورة': 150, 'العميل': 120, 'الهاتف': 100, 'الإجمالي': 80,
                    'طريقة الدفع': 100, 'التاريخ': 100, 'الوقت': 80}

    for col in columns:
        invoices_tree.heading(col, text=col)
        invoices_tree.column(col, width=column_widths.get(col, 100), anchor='center')

    # إضافة scrollbar
    scrollbar = ttk.Scrollbar(parent, orient="vertical", command=invoices_tree.yview)
    invoices_tree.configure(yscrollcommand=scrollbar.set)

    # تخطيط الجدول
    invoices_tree.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # تحميل البيانات
    load_invoices_data()

    # ربط النقر المزدوج لعرض تفاصيل الفاتورة
    invoices_tree.bind("<Double-1>", lambda event: show_invoice_details())

    return invoices_tree

def load_invoices_data():
    """تحميل بيانات الفواتير"""
    try:
        # مسح البيانات الموجودة
        for item in invoices_tree.get_children():
            invoices_tree.delete(item)

        db = connect_db()
        if db:
            cursor = db.cursor()
            # استعلام لجمع بيانات الفواتير
            query = """
            SELECT invoice_id, customer, phone, SUM(total) as total_amount,
                   payment_method, date, time
            FROM sales
            GROUP BY invoice_id, customer, phone, payment_method, date, time
            ORDER BY date DESC, time DESC
            """
            cursor.execute(query)
            rows = cursor.fetchall()

            for row in rows:
                invoices_tree.insert("", "end", values=(
                    row[0], row[1] or 'عميل', row[2] or '-',
                    f"{row[3]:.2f}", row[4], row[5], row[6]
                ))

            cursor.close()
            db.close()

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في تحميل الفواتير: {str(e)}")

def search_invoices():
    """البحث في الفواتير"""
    invoice_id = invoice_search_entry.get().strip()
    customer = customer_search_entry.get().strip()
    date = date_search_entry.get().strip()

    if date == "YYYY-MM-DD":
        date = ""

    try:
        # مسح البيانات الموجودة
        for item in invoices_tree.get_children():
            invoices_tree.delete(item)

        db = connect_db()
        if db:
            cursor = db.cursor()

            # بناء الاستعلام حسب معايير البحث
            conditions = []
            params = []

            if invoice_id:
                conditions.append("invoice_id LIKE %s")
                params.append(f"%{invoice_id}%")

            if customer:
                conditions.append("customer LIKE %s")
                params.append(f"%{customer}%")

            if date:
                conditions.append("date = %s")
                params.append(date)

            where_clause = " AND ".join(conditions) if conditions else "1=1"

            query = f"""
            SELECT invoice_id, customer, phone, SUM(total) as total_amount,
                   payment_method, date, time
            FROM sales
            WHERE {where_clause}
            GROUP BY invoice_id, customer, phone, payment_method, date, time
            ORDER BY date DESC, time DESC
            """

            cursor.execute(query, params)
            rows = cursor.fetchall()

            for row in rows:
                invoices_tree.insert("", "end", values=(
                    row[0], row[1] or 'عميل', row[2] or '-',
                    f"{row[3]:.2f}", row[4], row[5], row[6]
                ))

            cursor.close()
            db.close()

            if not rows:
                messagebox.showinfo("نتيجة البحث", "لم يتم العثور على فواتير تطابق معايير البحث")

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")

def show_invoice_details():
    """عرض تفاصيل الفاتورة"""
    selected = invoices_tree.selection()
    if not selected:
        messagebox.showwarning("تحذير", "يرجى اختيار فاتورة لعرض تفاصيلها")
        return

    # الحصول على رقم الفاتورة
    item = invoices_tree.item(selected[0])
    invoice_id = item['values'][0]

    # إنشاء نافذة التفاصيل
    details_window = tk.Toplevel(root)
    details_window.title(f"تفاصيل الفاتورة - {invoice_id}")
    details_window.geometry("600x500")
    details_window.configure(bg=COLORS['bg_card'])
    details_window.resizable(False, False)

    details_window.transient(root)
    details_window.grab_set()

    # العنوان
    tk.Label(details_window, text=f"تفاصيل الفاتورة: {invoice_id}",
             font=FONTS['title'], bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=20)

    try:
        db = connect_db()
        if db:
            cursor = db.cursor()
            cursor.execute("SELECT * FROM sales WHERE invoice_id = %s", (invoice_id,))
            rows = cursor.fetchall()

            if rows:
                # معلومات الفاتورة
                first_row = rows[0]
                info_frame = tk.Frame(details_window, bg=COLORS['bg_card'])
                info_frame.pack(fill="x", padx=20, pady=10)

                tk.Label(info_frame, text=f"العميل: {first_row[7] or 'عميل'}",
                        font=FONTS['main'], bg=COLORS['bg_card']).pack(anchor="w")
                tk.Label(info_frame, text=f"الهاتف: {first_row[8] or '-'}",
                        font=FONTS['main'], bg=COLORS['bg_card']).pack(anchor="w")
                tk.Label(info_frame, text=f"طريقة الدفع: {first_row[6]}",
                        font=FONTS['main'], bg=COLORS['bg_card']).pack(anchor="w")
                tk.Label(info_frame, text=f"التاريخ: {first_row[9]} - الوقت: {first_row[5]}",
                        font=FONTS['main'], bg=COLORS['bg_card']).pack(anchor="w")

                # جدول المنتجات
                from tkinter import ttk
                products_frame = tk.Frame(details_window, bg=COLORS['bg_card'])
                products_frame.pack(fill="both", expand=True, padx=20, pady=10)

                columns = ('المنتج', 'السعر', 'الكمية', 'الإجمالي')
                tree = ttk.Treeview(products_frame, columns=columns, show='headings', height=10)

                for col in columns:
                    tree.heading(col, text=col)
                    tree.column(col, width=120, anchor='center')

                scrollbar = ttk.Scrollbar(products_frame, orient="vertical", command=tree.yview)
                tree.configure(yscrollcommand=scrollbar.set)

                tree.pack(side="left", fill="both", expand=True)
                scrollbar.pack(side="right", fill="y")

                # إضافة المنتجات
                total_amount = 0
                for row in rows:
                    tree.insert("", "end", values=(row[2], f"{row[3]:.2f}", row[4], f"{row[5]:.2f}"))
                    total_amount += row[5]

                # الإجمالي
                tk.Label(details_window, text=f"الإجمالي النهائي: {total_amount:.2f}",
                        font=FONTS['heading'], bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=20)

                # أزرار الطباعة
                buttons_frame = tk.Frame(details_window, bg=COLORS['bg_card'])
                buttons_frame.pack(pady=10)

                # زر طباعة PDF
                pdf_btn = tk.Button(buttons_frame, text="طباعة PDF",
                                   command=lambda: print_invoice_pdf(invoice_id),
                                   bg=COLORS['btn_success'], fg=COLORS['text_white'], font=FONTS['button'])
                pdf_btn.pack(side="left", padx=5)

                # زر الطباعة النصية
                print_btn = tk.Button(buttons_frame, text="طباعة نصية",
                                     command=lambda: print_single_invoice(invoice_id),
                                     bg=COLORS['accent'], fg=COLORS['text_white'], font=FONTS['button'])
                print_btn.pack(side="left", padx=5)

            cursor.close()
            db.close()

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في تحميل تفاصيل الفاتورة: {str(e)}")

    # زر الإغلاق
    close_btn = tk.Button(details_window, text="إغلاق", command=details_window.destroy,
                         bg=COLORS['accent'], fg=COLORS['text_white'], font=FONTS['button'])
    close_btn.pack(pady=10)

def print_invoice_pdf(invoice_id):
    """طباعة فاتورة واحدة بصيغة PDF"""
    try:
        db = connect_db()
        if db:
            cursor = db.cursor()
            cursor.execute("SELECT * FROM sales WHERE invoice_id = %s", (invoice_id,))
            rows = cursor.fetchall()

            if rows:
                # تجميع بيانات الفاتورة
                first_row = rows[0]
                customer_name = first_row[7]  # customer
                phone = first_row[8]  # phone
                payment_method = first_row[6]  # payment_method
                date = first_row[9]  # date
                time = first_row[5]  # time

                # تجميع المنتجات
                items = []
                total_amount = 0
                for row in rows:
                    product_name = row[1]  # product_name
                    price = row[2]  # price
                    quantity = row[3]  # qty
                    total = row[4]  # total
                    items.append((product_name, price, quantity, total))
                    total_amount += total

                # إنشاء بيانات الفاتورة
                invoice_data = {
                    'invoice_id': invoice_id,
                    'items': items,
                    'customer': customer_name,
                    'phone': phone,
                    'payment_method': payment_method,
                    'total': total_amount,
                    'date': str(date),
                    'time': str(time)
                }

                # إنشاء PDF
                create_pdf_invoice(invoice_data)
            else:
                messagebox.showerror("خطأ", "لم يتم العثور على تفاصيل الفاتورة")

            cursor.close()
            db.close()
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء PDF: {str(e)}")

def print_single_invoice(invoice_id):
    """طباعة فاتورة واحدة"""
    try:
        db = connect_db()
        if db:
            cursor = db.cursor()
            cursor.execute("SELECT * FROM sales WHERE invoice_id = %s", (invoice_id,))
            rows = cursor.fetchall()

            if rows:
                first_row = rows[0]

                print("\n" + "="*60)
                print("                    صيدلية الشفاء")
                print("                   فاتورة مبيعات")
                print("="*60)
                print(f"رقم الفاتورة: {invoice_id}")
                print(f"العميل: {first_row[7] or 'عميل'}")
                print(f"الهاتف: {first_row[8] or '-'}")
                print(f"طريقة الدفع: {first_row[6]}")
                print(f"التاريخ: {first_row[9]} - الوقت: {first_row[5]}")
                print("-"*60)
                print(f"{'المنتج':<25} {'السعر':<10} {'الكمية':<8} {'الإجمالي':<12}")
                print("-"*60)

                total_amount = 0
                for row in rows:
                    print(f"{row[2]:<25} {row[3]:<10.2f} {row[4]:<8} {row[5]:<12.2f}")
                    total_amount += row[5]

                print("-"*60)
                print(f"{'الإجمالي النهائي:':<44} {total_amount:<12.2f}")
                print("="*60)
                print("                  شكراً لتعاملكم معنا")
                print("="*60)

                messagebox.showinfo("تم", "تم طباعة الفاتورة في الطرفية")

            cursor.close()
            db.close()

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في طباعة الفاتورة: {str(e)}")

def print_all_invoices_report():
    """طباعة تقرير شامل لجميع الفواتير"""
    try:
        db = connect_db()
        if db:
            cursor = db.cursor()

            # إحصائيات عامة
            cursor.execute("SELECT COUNT(DISTINCT invoice_id), SUM(total) FROM sales")
            total_data = cursor.fetchone()
            total_invoices = total_data[0] or 0
            total_sales = total_data[1] or 0

            # الفواتير
            cursor.execute("""
            SELECT invoice_id, customer, phone, SUM(total) as total_amount,
                   payment_method, date, time
            FROM sales
            GROUP BY invoice_id, customer, phone, payment_method, date, time
            ORDER BY date DESC, time DESC
            """)
            invoices = cursor.fetchall()

            print("\n" + "="*80)
            print("                         صيدلية الشفاء")
            print("                      تقرير شامل للفواتير")
            print("="*80)
            print(f"إجمالي عدد الفواتير: {total_invoices}")
            print(f"إجمالي المبيعات: {total_sales:.2f}")
            print(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("="*80)
            print(f"{'رقم الفاتورة':<20} {'العميل':<15} {'الإجمالي':<10} {'طريقة الدفع':<12} {'التاريخ':<12}")
            print("-"*80)

            for invoice in invoices:
                print(f"{invoice[0]:<20} {(invoice[1] or 'عميل'):<15} {invoice[3]:<10.2f} {invoice[4]:<12} {invoice[5]:<12}")

            print("="*80)
            print(f"{'إجمالي المبيعات:':<60} {total_sales:<15.2f}")
            print("="*80)

            cursor.close()
            db.close()

            messagebox.showinfo("تم", "تم طباعة التقرير الشامل في الطرفية")

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في طباعة التقرير: {str(e)}")

def show_users():
    """عرض إدارة المستخدمين الكاملة"""
    clear_content()

    # عنوان الصفحة
    title_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    title_frame.pack(fill="x", pady=10)

    tk.Label(title_frame, text="إدارة المستخدمين", font=FONTS['title'],
             bg=COLORS['bg_main'], fg=COLORS['primary']).pack(side="left", padx=20)

    # زر إضافة مستخدم جديد
    add_user_btn = tk.Button(title_frame, text="+ إضافة مستخدم جديد", command=add_user_window,
                            bg=COLORS['btn_success'], fg=COLORS['text_white'], font=FONTS['button'])
    add_user_btn.pack(side="right", padx=20)

    # إطار البحث
    search_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    search_frame.pack(fill="x", padx=20, pady=10)

    tk.Label(search_frame, text="البحث باسم المستخدم:", font=FONTS['main'], bg=COLORS['bg_main']).pack(side="left")
    global user_search_entry
    user_search_entry = tk.Entry(search_frame, font=FONTS['main'], width=25)
    user_search_entry.pack(side="left", padx=10)

    search_users_btn = tk.Button(search_frame, text="بحث", command=search_users,
                                bg=COLORS['accent'], fg=COLORS['text_white'], font=FONTS['button'])
    search_users_btn.pack(side="left", padx=5)

    refresh_users_btn = tk.Button(search_frame, text="تحديث", command=show_users,
                                 bg=COLORS['primary'], fg=COLORS['text_white'], font=FONTS['button'])
    refresh_users_btn.pack(side="left", padx=5)

    # إطار الجدول
    table_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    table_frame.pack(fill="both", expand=True, padx=20, pady=10)

    # إنشاء جدول المستخدمين
    create_users_table(table_frame)

def create_users_table(parent):
    """إنشاء جدول المستخدمين"""
    from tkinter import ttk

    # إنشاء Treeview
    columns = ('اسم المستخدم', 'الصلاحية', 'تاريخ الإنشاء', 'آخر دخول')
    global users_tree
    users_tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)

    # تعريف العناوين
    for col in columns:
        users_tree.heading(col, text=col)
        users_tree.column(col, width=150, anchor='center')

    # إضافة scrollbar
    scrollbar = ttk.Scrollbar(parent, orient="vertical", command=users_tree.yview)
    users_tree.configure(yscrollcommand=scrollbar.set)

    # تخطيط الجدول
    users_tree.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # تحميل البيانات
    load_users_data()

    # ربط النقر المزدوج لتعديل المستخدم
    users_tree.bind("<Double-1>", lambda event: edit_user())

    return users_tree

def load_users_data():
    """تحميل بيانات المستخدمين"""
    try:
        # مسح البيانات الموجودة
        for item in users_tree.get_children():
            users_tree.delete(item)

        db = connect_db()
        if db:
            cursor = db.cursor()
            cursor.execute("SELECT username, role, created_at, last_login FROM users ORDER BY username")
            rows = cursor.fetchall()

            for row in rows:
                role_name = "مدير النظام" if row[1] == 'admin' else "مستخدم محدود"
                created_at = row[2].strftime('%Y-%m-%d') if row[2] else 'غير محدد'
                last_login = row[3].strftime('%Y-%m-%d %H:%M') if row[3] else 'لم يسجل دخول'

                users_tree.insert("", "end", values=(row[0], role_name, created_at, last_login))

            cursor.close()
            db.close()

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في تحميل المستخدمين: {str(e)}")

def search_users():
    """البحث في المستخدمين"""
    search_term = user_search_entry.get().strip()

    if not search_term:
        show_users()
        return

    try:
        # مسح البيانات الموجودة
        for item in users_tree.get_children():
            users_tree.delete(item)

        db = connect_db()
        if db:
            cursor = db.cursor()
            search_query = f"%{search_term}%"
            cursor.execute("SELECT username, role, created_at, last_login FROM users WHERE username LIKE %s ORDER BY username", (search_query,))
            rows = cursor.fetchall()

            for row in rows:
                role_name = "مدير النظام" if row[1] == 'admin' else "مستخدم محدود"
                created_at = row[2].strftime('%Y-%m-%d') if row[2] else 'غير محدد'
                last_login = row[3].strftime('%Y-%m-%d %H:%M') if row[3] else 'لم يسجل دخول'

                users_tree.insert("", "end", values=(row[0], role_name, created_at, last_login))

            cursor.close()
            db.close()

            if not rows:
                messagebox.showinfo("نتيجة البحث", "لم يتم العثور على مستخدمين")

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")

def add_user_window():
    """نافذة إضافة مستخدم جديد"""
    add_window = tk.Toplevel(root)
    add_window.title("إضافة مستخدم جديد")
    add_window.geometry("400x350")
    add_window.configure(bg=COLORS['bg_card'])
    add_window.resizable(False, False)

    # توسيط النافذة
    add_window.transient(root)
    add_window.grab_set()

    # العنوان
    tk.Label(add_window, text="إضافة مستخدم جديد", font=FONTS['title'],
             bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=20)

    # إطار الحقول
    fields_frame = tk.Frame(add_window, bg=COLORS['bg_card'])
    fields_frame.pack(padx=30, pady=20, fill="both", expand=True)

    # اسم المستخدم
    tk.Label(fields_frame, text="اسم المستخدم:", font=FONTS['main'],
            bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", pady=(10, 5))
    username_entry = tk.Entry(fields_frame, font=FONTS['main'], width=30, relief="solid", bd=1)
    username_entry.pack(fill="x", pady=(0, 10))

    # كلمة المرور
    tk.Label(fields_frame, text="كلمة المرور:", font=FONTS['main'],
            bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", pady=(10, 5))
    password_entry = tk.Entry(fields_frame, font=FONTS['main'], width=30, relief="solid", bd=1, show="*")
    password_entry.pack(fill="x", pady=(0, 10))

    # تأكيد كلمة المرور
    tk.Label(fields_frame, text="تأكيد كلمة المرور:", font=FONTS['main'],
            bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", pady=(10, 5))
    confirm_password_entry = tk.Entry(fields_frame, font=FONTS['main'], width=30, relief="solid", bd=1, show="*")
    confirm_password_entry.pack(fill="x", pady=(0, 10))

    # الصلاحية
    tk.Label(fields_frame, text="الصلاحية:", font=FONTS['main'],
            bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", pady=(10, 5))

    role_var = tk.StringVar(value="user")
    role_frame = tk.Frame(fields_frame, bg=COLORS['bg_card'])
    role_frame.pack(fill="x", pady=(0, 10))

    tk.Radiobutton(role_frame, text="مستخدم محدود", variable=role_var, value="user",
                  bg=COLORS['bg_card'], font=FONTS['main']).pack(anchor="w")
    tk.Radiobutton(role_frame, text="مدير النظام", variable=role_var, value="admin",
                  bg=COLORS['bg_card'], font=FONTS['main']).pack(anchor="w")

    # إطار الأزرار
    buttons_frame = tk.Frame(add_window, bg=COLORS['bg_card'])
    buttons_frame.pack(pady=20)

    # زر الحفظ
    save_btn = tk.Button(buttons_frame, text="إضافة المستخدم",
                        command=lambda: save_new_user(username_entry, password_entry, confirm_password_entry, role_var, add_window),
                        bg=COLORS['btn_success'], fg=COLORS['text_white'],
                        font=FONTS['button'], width=15)
    save_btn.pack(side="left", padx=10)

    # زر الإلغاء
    cancel_btn = tk.Button(buttons_frame, text="إلغاء", command=add_window.destroy,
                          bg=COLORS['error'], fg=COLORS['text_white'],
                          font=FONTS['button'], width=15)
    cancel_btn.pack(side="left", padx=10)

def save_new_user(username_entry, password_entry, confirm_password_entry, role_var, window):
    """حفظ مستخدم جديد"""
    try:
        # الحصول على البيانات
        username = username_entry.get().strip()
        password = password_entry.get().strip()
        confirm_password = confirm_password_entry.get().strip()
        role = role_var.get()

        # التحقق من البيانات
        if not all([username, password, confirm_password]):
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
            return

        if len(username) < 3:
            messagebox.showerror("خطأ", "اسم المستخدم يجب أن يكون 3 أحرف على الأقل")
            return

        if len(password) < 6:
            messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
            return

        if password != confirm_password:
            messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
            return

        # حفظ في قاعدة البيانات
        db = connect_db()
        if db:
            cursor = db.cursor()

            # التحقق من عدم وجود مستخدم بنفس الاسم
            cursor.execute("SELECT id FROM users WHERE username = %s", (username,))
            if cursor.fetchone():
                messagebox.showerror("خطأ", "يوجد مستخدم بنفس الاسم مسبقاً")
                cursor.close()
                db.close()
                return

            # إدراج المستخدم الجديد
            from datetime import datetime
            now = datetime.now()
            cursor.execute("INSERT INTO users (username, password, role, created_at) VALUES (%s, %s, %s, %s)",
                          (username, password, role, now))

            db.commit()
            cursor.close()
            db.close()

            messagebox.showinfo("نجح", "تم إضافة المستخدم بنجاح!")
            window.destroy()
            show_users()  # تحديث قائمة المستخدمين

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في إضافة المستخدم: {str(e)}")

def edit_user():
    """تعديل مستخدم محدد"""
    selected = users_tree.selection()
    if not selected:
        messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للتعديل")
        return

    # الحصول على بيانات المستخدم المحدد
    item = users_tree.item(selected[0])
    values = item['values']
    username = values[0]

    # التحقق من عدم تعديل المستخدم الحالي لنفسه
    if username == current_user['username']:
        messagebox.showwarning("تحذير", "لا يمكنك تعديل بياناتك الخاصة من هنا")
        return

    # إنشاء نافذة التعديل
    edit_window = tk.Toplevel(root)
    edit_window.title(f"تعديل المستخدم - {username}")
    edit_window.geometry("400x400")
    edit_window.configure(bg=COLORS['bg_card'])
    edit_window.resizable(False, False)

    edit_window.transient(root)
    edit_window.grab_set()

    # العنوان
    tk.Label(edit_window, text=f"تعديل المستخدم: {username}", font=FONTS['title'],
             bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=20)

    # إطار الحقول
    fields_frame = tk.Frame(edit_window, bg=COLORS['bg_card'])
    fields_frame.pack(padx=30, pady=20, fill="both", expand=True)

    # كلمة المرور الجديدة
    tk.Label(fields_frame, text="كلمة المرور الجديدة (اتركها فارغة للاحتفاظ بالحالية):",
             font=FONTS['main'], bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", pady=(10, 5))
    new_password_entry = tk.Entry(fields_frame, font=FONTS['main'], width=30, relief="solid", bd=1, show="*")
    new_password_entry.pack(fill="x", pady=(0, 10))

    # تأكيد كلمة المرور الجديدة
    tk.Label(fields_frame, text="تأكيد كلمة المرور الجديدة:", font=FONTS['main'],
             bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", pady=(10, 5))
    confirm_new_password_entry = tk.Entry(fields_frame, font=FONTS['main'], width=30, relief="solid", bd=1, show="*")
    confirm_new_password_entry.pack(fill="x", pady=(0, 10))

    # الصلاحية الحالية
    current_role = "admin" if values[1] == "مدير النظام" else "user"

    tk.Label(fields_frame, text="الصلاحية:", font=FONTS['main'],
             bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(anchor="w", pady=(10, 5))

    role_var = tk.StringVar(value=current_role)
    role_frame = tk.Frame(fields_frame, bg=COLORS['bg_card'])
    role_frame.pack(fill="x", pady=(0, 10))

    tk.Radiobutton(role_frame, text="مستخدم محدود", variable=role_var, value="user",
                   bg=COLORS['bg_card'], font=FONTS['main']).pack(anchor="w")
    tk.Radiobutton(role_frame, text="مدير النظام", variable=role_var, value="admin",
                   bg=COLORS['bg_card'], font=FONTS['main']).pack(anchor="w")

    # إطار الأزرار
    buttons_frame = tk.Frame(edit_window, bg=COLORS['bg_card'])
    buttons_frame.pack(pady=20)

    # زر الحفظ
    save_btn = tk.Button(buttons_frame, text="حفظ التغييرات",
                        command=lambda: update_user(username, new_password_entry, confirm_new_password_entry, role_var, edit_window),
                        bg=COLORS['btn_success'], fg=COLORS['text_white'],
                        font=FONTS['button'], width=15)
    save_btn.pack(side="left", padx=10)

    # زر الحذف
    delete_btn = tk.Button(buttons_frame, text="حذف المستخدم",
                          command=lambda: delete_user(username, edit_window),
                          bg=COLORS['error'], fg=COLORS['text_white'],
                          font=FONTS['button'], width=15)
    delete_btn.pack(side="left", padx=10)

    # زر الإلغاء
    cancel_btn = tk.Button(buttons_frame, text="إلغاء", command=edit_window.destroy,
                          bg=COLORS['accent'], fg=COLORS['text_white'],
                          font=FONTS['button'], width=15)
    cancel_btn.pack(side="left", padx=10)

def update_user(username, new_password_entry, confirm_new_password_entry, role_var, window):
    """تحديث بيانات المستخدم"""
    try:
        new_password = new_password_entry.get().strip()
        confirm_new_password = confirm_new_password_entry.get().strip()
        role = role_var.get()

        # التحقق من كلمة المرور إذا تم إدخالها
        if new_password:
            if len(new_password) < 6:
                messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
                return

            if new_password != confirm_new_password:
                messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
                return

        # تحديث في قاعدة البيانات
        db = connect_db()
        if db:
            cursor = db.cursor()

            if new_password:
                # تحديث كلمة المرور والصلاحية
                cursor.execute("UPDATE users SET password = %s, role = %s WHERE username = %s",
                              (new_password, role, username))
            else:
                # تحديث الصلاحية فقط
                cursor.execute("UPDATE users SET role = %s WHERE username = %s",
                              (role, username))

            db.commit()
            cursor.close()
            db.close()

            messagebox.showinfo("نجح", "تم تحديث المستخدم بنجاح!")
            window.destroy()
            show_users()  # تحديث قائمة المستخدمين

    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في تحديث المستخدم: {str(e)}")

def delete_user(username, window):
    """حذف مستخدم"""
    # التحقق من عدم حذف المستخدم الحالي
    if username == current_user['username']:
        messagebox.showerror("خطأ", "لا يمكنك حذف حسابك الخاص")
        return

    result = messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف المستخدم '{username}' نهائياً؟")
    if result:
        try:
            db = connect_db()
            if db:
                cursor = db.cursor()
                cursor.execute("DELETE FROM users WHERE username = %s", (username,))
                db.commit()
                cursor.close()
                db.close()

                messagebox.showinfo("نجح", "تم حذف المستخدم بنجاح!")
                window.destroy()
                show_users()  # تحديث قائمة المستخدمين

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف المستخدم: {str(e)}")

def test_database():
    """اختبار قاعدة البيانات"""
    clear_content()
    tk.Label(main_content, text="اختبار قاعدة البيانات", font=FONTS['title'],
             bg=COLORS['bg_main'], fg=COLORS['primary']).pack(pady=30)
    
    result_frame = tk.Frame(main_content, bg=COLORS['bg_main'])
    result_frame.pack(pady=20)
    
    db = connect_db()
    if db:
        try:
            cursor = db.cursor()
            cursor.execute("SELECT COUNT(*) FROM users")
            users_count = cursor.fetchone()[0]
            cursor.close()
            db.close()
            
            tk.Label(result_frame, text="الاتصال بقاعدة البيانات ناجح",
                    font=FONTS['heading'], bg=COLORS['bg_main'], fg=COLORS['btn_success']).pack(pady=10)
            tk.Label(result_frame, text=f"عدد المستخدمين في قاعدة البيانات: {users_count}",
                    font=FONTS['main'], bg=COLORS['bg_main']).pack(pady=5)
        except Exception as e:
            tk.Label(result_frame, text=f"خطأ في قاعدة البيانات: {str(e)}",
                    font=FONTS['main'], bg=COLORS['bg_main'], fg=COLORS['error']).pack(pady=10)
    else:
        tk.Label(result_frame, text="فشل في الاتصال بقاعدة البيانات",
                font=FONTS['heading'], bg=COLORS['bg_main'], fg=COLORS['error']).pack(pady=10)
        tk.Label(result_frame, text="تأكد من تشغيل MySQL Server",
                font=FONTS['main'], bg=COLORS['bg_main'], fg=COLORS['error']).pack(pady=5)

def logout():
    """تسجيل الخروج"""
    result = messagebox.askyesno("تسجيل خروج", "هل تريد تسجيل الخروج من النظام؟")
    if result:
        # إعادة تشغيل النظام
        root.destroy()
        main()

def main():
    """الدالة الرئيسية"""
    global root, login_frame, username_entry, password_entry

    # إنشاء النافذة الرئيسية
    root = tk.Tk()
    root.title("صيدلية الشفاء - نظام إدارة شامل")

    # الحصول على أبعاد الشاشة
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()

    # تحديد حجم النافذة بناءً على حجم الشاشة
    if screen_width >= 1920:  # شاشة كبيرة
        window_width = 1400
        window_height = 800
    elif screen_width >= 1366:  # شاشة متوسطة
        window_width = 1200
        window_height = 700
    else:  # شاشة صغيرة
        window_width = int(screen_width * 0.9)
        window_height = int(screen_height * 0.8)

    # تطبيق الحجم المناسب
    root.geometry(f"{window_width}x{window_height}")
    root.configure(bg=COLORS['bg_main'])

    # توسيط النافذة
    x = (screen_width - window_width) // 2
    y = (screen_height - window_height) // 2
    root.geometry(f'{window_width}x{window_height}+{x}+{y}')

    # جعل النافذة قابلة لتغيير الحجم
    root.resizable(True, True)

    # تحديد الحد الأدنى لحجم النافذة
    root.minsize(1000, 600)
    
    # شاشة تسجيل الدخول
    login_frame = tk.Frame(root, bg=COLORS['bg_main'])
    login_frame.pack(fill="both", expand=True)
    
    # إطار تسجيل الدخول المتجاوب
    # تحديد حجم إطار تسجيل الدخول بناءً على حجم الشاشة
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()

    if screen_width >= 1920:
        login_width = 550
        login_height = 500
    elif screen_width >= 1366:
        login_width = 500
        login_height = 450
    else:
        login_width = 450
        login_height = 400

    login_container = tk.Frame(login_frame, bg=COLORS['bg_card'], relief="raised", bd=3)
    login_container.place(relx=0.5, rely=0.5, anchor="center", width=login_width, height=login_height)

    # شريط علوي ملون
    top_bar = tk.Frame(login_container, bg=COLORS['primary'], height=8)
    top_bar.pack(fill="x")

    # العنوان المحسن مع أيقونة
    title_frame = tk.Frame(login_container, bg=COLORS['bg_card'])
    title_frame.pack(pady=25)

    tk.Label(title_frame, text=f"{ICONS['pharmacy']}", font=('Arial', 32),
             bg=COLORS['bg_card'], fg=COLORS['primary']).pack()

    tk.Label(title_frame, text="صيدلية الشفاء", font=FONTS['title'],
             bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=(10, 5))

    tk.Label(title_frame, text="نظام إدارة شامل ومتطور", font=FONTS['main'],
             bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack()

    # خط فاصل
    separator = tk.Frame(login_container, height=2, bg=COLORS['accent'])
    separator.pack(fill="x", padx=50, pady=15)

    tk.Label(login_container, text=f"{ICONS['users']} تسجيل الدخول", font=FONTS['heading'],
             bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(pady=10)
    
    # حقول الإدخال المحسنة
    fields_frame = tk.Frame(login_container, bg=COLORS['bg_card'])
    fields_frame.pack(pady=20, padx=40, fill="x")

    # حقل اسم المستخدم
    user_frame = tk.Frame(fields_frame, bg=COLORS['bg_card'])
    user_frame.pack(fill="x", pady=12)

    tk.Label(user_frame, text=f"{ICONS['users']} اسم المستخدم:",
             font=('Arial', 14, 'bold'), bg=COLORS['bg_card'],
             fg=COLORS['primary']).pack(anchor="w", pady=(0, 8))

    # إطار حقل اسم المستخدم مع زر
    username_input_frame = tk.Frame(user_frame, bg=COLORS['bg_card'])
    username_input_frame.pack(fill="x", pady=(5, 0))

    username_entry = tk.Entry(username_input_frame, font=('Arial', 13), relief="solid", bd=2,
                             highlightthickness=2, highlightcolor=COLORS['primary'],
                             highlightbackground=COLORS['accent'], insertbackground=COLORS['primary'],
                             selectbackground=COLORS['primary'], selectforeground=COLORS['text_white'])
    username_entry.pack(side="left", fill="x", expand=True, ipady=12)

    # زر مسح اسم المستخدم المحسن
    clear_username_btn = tk.Button(username_input_frame, text="🗑️ مسح",
                                  font=('Arial', 12, 'bold'),
                                  bg=COLORS['btn_danger'], fg=COLORS['text_white'],
                                  relief="raised", bd=2, width=8, height=1,
                                  cursor="hand2", activebackground=COLORS['error'],
                                  activeforeground=COLORS['text_white'],
                                  command=lambda: username_entry.delete(0, tk.END))
    clear_username_btn.pack(side="right", padx=(12, 0), pady=2)

    # tooltip للزر (محدث)
    def show_clear_tooltip(event):
        clear_username_btn.configure(text="🗑️ مسح الكل")

    def hide_clear_tooltip(event):
        clear_username_btn.configure(text="🗑️ مسح")

    clear_username_btn.bind("<Button-3>", show_clear_tooltip)  # النقر بالزر الأيمن
    clear_username_btn.bind("<Leave>", hide_clear_tooltip)

    # تأثير hover محسن لزر المسح
    def on_clear_user_enter(event):
        clear_username_btn.configure(bg=COLORS['error'], relief="solid", bd=4,
                                    text="🗑️ مسح الآن")

    def on_clear_user_leave(event):
        clear_username_btn.configure(bg=COLORS['btn_danger'], relief="raised", bd=3,
                                    text="🗑️ مسح")

    clear_username_btn.bind("<Enter>", on_clear_user_enter)
    clear_username_btn.bind("<Leave>", on_clear_user_leave)

    # تأثيرات للحقل الأول
    def on_username_focus_in(event):
        username_entry.configure(highlightcolor=COLORS['primary'], highlightthickness=2)

    def on_username_focus_out(event):
        username_entry.configure(highlightcolor=COLORS['accent'], highlightthickness=1)

    username_entry.bind("<FocusIn>", on_username_focus_in)
    username_entry.bind("<FocusOut>", on_username_focus_out)

    # حقل كلمة المرور
    pass_frame = tk.Frame(fields_frame, bg=COLORS['bg_card'])
    pass_frame.pack(fill="x", pady=12)

    tk.Label(pass_frame, text=f"{ICONS['settings']} كلمة المرور:",
             font=('Arial', 14, 'bold'), bg=COLORS['bg_card'],
             fg=COLORS['primary']).pack(anchor="w", pady=(0, 8))

    # إطار حقل كلمة المرور مع زر
    password_input_frame = tk.Frame(pass_frame, bg=COLORS['bg_card'])
    password_input_frame.pack(fill="x", pady=(5, 0))

    password_entry = tk.Entry(password_input_frame, font=('Arial', 13), relief="solid", bd=2, show="*",
                             highlightthickness=2, highlightcolor=COLORS['primary'],
                             highlightbackground=COLORS['accent'], insertbackground=COLORS['primary'],
                             selectbackground=COLORS['primary'], selectforeground=COLORS['text_white'])
    password_entry.pack(side="left", fill="x", expand=True, ipady=12)

    # متغير لحالة إظهار كلمة المرور
    password_visible = tk.BooleanVar(value=False)

    # زر إظهار/إخفاء كلمة المرور
    def toggle_password():
        if password_visible.get():
            password_entry.configure(show="*")
            show_password_btn.configure(text="👁️ إظهار", bg=COLORS['accent'])
            password_hint.configure(text="💡 اضغط على زر 'إظهار' لرؤية كلمة المرور")
            password_visible.set(False)
        else:
            password_entry.configure(show="")
            show_password_btn.configure(text="🙈 إخفاء", bg=COLORS['primary'])
            password_hint.configure(text="🔓 كلمة المرور مرئية الآن - اضغط 'إخفاء' لإخفائها")
            password_visible.set(True)

    show_password_btn = tk.Button(password_input_frame, text="👁️ إظهار",
                                 font=('Arial', 13, 'bold'),
                                 bg=COLORS['accent'], fg=COLORS['text_white'],
                                 relief="raised", bd=3, width=12, height=1,
                                 cursor="hand2", activebackground=COLORS['info'],
                                 activeforeground=COLORS['text_white'],
                                 command=toggle_password)
    show_password_btn.pack(side="right", padx=(15, 0), pady=2)

    # تأثير hover محسن لزر إظهار كلمة المرور
    def on_show_pass_enter(event):
        if password_visible.get():
            show_password_btn.configure(bg=COLORS['btn_success'], relief="solid", bd=3,
                                      text="🙈 إخفاء الآن", font=('Arial', 13, 'bold'))
        else:
            show_password_btn.configure(bg=COLORS['info'], relief="solid", bd=3,
                                      text="👁️ إظهار الآن", font=('Arial', 13, 'bold'))

    def on_show_pass_leave(event):
        if password_visible.get():
            show_password_btn.configure(bg=COLORS['primary'], relief="raised", bd=2,
                                      text="🙈 إخفاء", font=('Arial', 12, 'bold'))
        else:
            show_password_btn.configure(bg=COLORS['accent'], relief="raised", bd=2,
                                      text="👁️ إظهار", font=('Arial', 12, 'bold'))

    show_password_btn.bind("<Enter>", on_show_pass_enter)
    show_password_btn.bind("<Leave>", on_show_pass_leave)

    # تأثيرات للحقل الثاني
    def on_password_focus_in(event):
        password_entry.configure(highlightcolor=COLORS['primary'], highlightthickness=2)

    def on_password_focus_out(event):
        password_entry.configure(highlightcolor=COLORS['accent'], highlightthickness=1)

    password_entry.bind("<FocusIn>", on_password_focus_in)
    password_entry.bind("<FocusOut>", on_password_focus_out)

    # تلميح لزر إظهار كلمة المرور
    password_hint = tk.Label(pass_frame, text="💡 اضغط على زر 'إظهار' لرؤية كلمة المرور",
                            font=('Arial', 11, 'italic'), bg=COLORS['bg_card'],
                            fg=COLORS['text_light'])
    password_hint.pack(anchor="w", pady=(8, 0))

    # زر تسجيل الدخول المحسن والجذاب
    login_btn = tk.Button(login_container, text=f"{ICONS['success']} دخول إلى النظام",
                         command=login, bg=COLORS['btn_success'], fg=COLORS['text_white'],
                         font=('Arial', 16, 'bold'), width=25, height=2,
                         relief="raised", bd=3, cursor="hand2",
                         activebackground=COLORS['primary'],
                         activeforeground=COLORS['text_white'])
    login_btn.pack(pady=30)

    # تأثير hover محسن لزر الدخول
    def on_login_enter(event):
        login_btn.configure(bg=COLORS['primary'], relief="solid", bd=5,
                          text=f"{ICONS['success']} دخول إلى النظام الآن",
                          font=('Arial', 15, 'bold'))

    def on_login_leave(event):
        login_btn.configure(bg=COLORS['btn_success'], relief="raised", bd=4,
                          text=f"{ICONS['success']} دخول إلى النظام",
                          font=('Arial', 14, 'bold'))

    login_btn.bind("<Enter>", on_login_enter)
    login_btn.bind("<Leave>", on_login_leave)
    
    # معلومات تسجيل الدخول المحسنة
    info_frame = tk.Frame(login_container, bg=COLORS['light'], relief="solid", bd=1)
    info_frame.pack(pady=15, padx=30, fill="x")

    tk.Label(info_frame, text=f"{ICONS['info']} بيانات تسجيل الدخول:",
             font=FONTS['small'], bg=COLORS['light'], fg=COLORS['primary']).pack(pady=(8, 5))

    # بيانات المدير
    admin_frame = tk.Frame(info_frame, bg=COLORS['light'])
    admin_frame.pack(pady=2)
    tk.Label(admin_frame, text=f"{ICONS['settings']} المدير:",
             font=FONTS['small'], bg=COLORS['light'], fg=COLORS['text_primary']).pack(side="left")
    tk.Label(admin_frame, text="admin / admin123",
             font=('Arial', 9, 'bold'), bg=COLORS['light'], fg=COLORS['primary']).pack(side="left", padx=(5, 0))

    # بيانات المستخدم
    user_frame = tk.Frame(info_frame, bg=COLORS['light'])
    user_frame.pack(pady=(2, 8))
    tk.Label(user_frame, text=f"{ICONS['users']} المستخدم:",
             font=FONTS['small'], bg=COLORS['light'], fg=COLORS['text_primary']).pack(side="left")
    tk.Label(user_frame, text="user / user123",
             font=('Arial', 9, 'bold'), bg=COLORS['light'], fg=COLORS['accent']).pack(side="left", padx=(5, 0))
    
    # ربط Enter
    username_entry.bind("<Return>", lambda e: password_entry.focus())
    password_entry.bind("<Return>", lambda e: login())
    
    # تركيز على حقل اسم المستخدم
    username_entry.focus()

    # إضافة تأثير وميض للزر عند بدء التطبيق
    def blink_password_button():
        current_bg = show_password_btn.cget('bg')
        if current_bg == COLORS['accent']:
            show_password_btn.configure(bg=COLORS['info'])
            root.after(500, lambda: show_password_btn.configure(bg=COLORS['accent']))
        root.after(4000, blink_password_button)  # كرر كل 4 ثوان

    # بدء تأثير الوميض بعد ثانية من التشغيل
    root.after(1000, blink_password_button)

    print("تم تشغيل نظام صيدلية الشفاء")
    print("بيانات تسجيل الدخول:")
    print("   المدير: admin / admin123")
    print("   المستخدم: user / user123")
    
    root.mainloop()

if __name__ == "__main__":
    main()
