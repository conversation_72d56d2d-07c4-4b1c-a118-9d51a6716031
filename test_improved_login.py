# -*- coding: utf-8 -*-
"""
اختبار صفحة تسجيل الدخول المحسنة
"""

import tkinter as tk
from tkinter import messagebox

# إعدادات الألوان والخطوط
COLORS = {
    'bg_main': '#f8f9fa',
    'bg_card': '#ffffff', 
    'primary': '#2c3e50',
    'accent': '#3498db',
    'text_primary': '#2c3e50',
    'text_secondary': '#34495e',
    'text_light': '#7f8c8d',
    'text_white': '#ffffff',
    'btn_success': '#27ae60',
    'btn_danger': '#e74c3c',
    'error': '#c0392b',
    'info': '#3498db',
    'light': '#ecf0f1'
}

FONTS = {
    'title': ('Arial', 20, 'bold'),
    'heading': ('Arial', 16, 'bold'),
    'main': ('Arial', 12),
    'small': ('Arial', 10),
    'button': ('Arial', 12, 'bold')
}

ICONS = {
    'pharmacy': '🏥',
    'users': '👤',
    'settings': '⚙️',
    'success': '✅',
    'info': 'ℹ️'
}

def login():
    username = username_entry.get()
    password = password_entry.get()
    
    if not username or not password:
        messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
        return
    
    if (username == "admin" and password == "admin123") or (username == "user" and password == "user123"):
        messagebox.showinfo("نجح", f"مرحباً {username}! تم تسجيل الدخول بنجاح")
        root.destroy()
    else:
        messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")

# إنشاء النافذة الرئيسية
root = tk.Tk()
root.title("🏥 صيدلية الشفاء - تسجيل الدخول المحسن")
root.configure(bg=COLORS['bg_main'])

# تحديد حجم النافذة وتوسيطها
window_width = 1200
window_height = 700
screen_width = root.winfo_screenwidth()
screen_height = root.winfo_screenheight()
x = (screen_width - window_width) // 2
y = (screen_height - window_height) // 2
root.geometry(f'{window_width}x{window_height}+{x}+{y}')

# شاشة تسجيل الدخول
login_frame = tk.Frame(root, bg=COLORS['bg_main'])
login_frame.pack(fill="both", expand=True)

# إطار تسجيل الدخول المحسن
login_width = 550
login_height = 500

login_container = tk.Frame(login_frame, bg=COLORS['bg_card'], relief="raised", bd=3)
login_container.place(relx=0.5, rely=0.5, anchor="center", width=login_width, height=login_height)

# شريط علوي ملون
top_bar = tk.Frame(login_container, bg=COLORS['primary'], height=8)
top_bar.pack(fill="x")

# العنوان المحسن مع أيقونة
title_frame = tk.Frame(login_container, bg=COLORS['bg_card'])
title_frame.pack(pady=25)

tk.Label(title_frame, text=f"{ICONS['pharmacy']}", font=('Arial', 32),
         bg=COLORS['bg_card'], fg=COLORS['primary']).pack()

tk.Label(title_frame, text="صيدلية الشفاء", font=FONTS['title'],
         bg=COLORS['bg_card'], fg=COLORS['primary']).pack(pady=(10, 5))

tk.Label(title_frame, text="نظام إدارة شامل ومتطور", font=FONTS['main'],
         bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack()

# خط فاصل
separator = tk.Frame(login_container, height=2, bg=COLORS['accent'])
separator.pack(fill="x", padx=50, pady=15)

tk.Label(login_container, text=f"{ICONS['users']} تسجيل الدخول", font=FONTS['heading'],
         bg=COLORS['bg_card'], fg=COLORS['text_primary']).pack(pady=10)

# حقول الإدخال المحسنة
fields_frame = tk.Frame(login_container, bg=COLORS['bg_card'])
fields_frame.pack(pady=20, padx=40, fill="x")

# حقل اسم المستخدم
user_frame = tk.Frame(fields_frame, bg=COLORS['bg_card'])
user_frame.pack(fill="x", pady=12)

tk.Label(user_frame, text=f"{ICONS['users']} اسم المستخدم:",
         font=('Arial', 14, 'bold'), bg=COLORS['bg_card'],
         fg=COLORS['primary']).pack(anchor="w", pady=(0, 8))

# إطار حقل اسم المستخدم مع زر
username_input_frame = tk.Frame(user_frame, bg=COLORS['bg_card'])
username_input_frame.pack(fill="x", pady=(5, 0))

username_entry = tk.Entry(username_input_frame, font=('Arial', 13), relief="solid", bd=2,
                         highlightthickness=2, highlightcolor=COLORS['primary'],
                         highlightbackground=COLORS['accent'], insertbackground=COLORS['primary'],
                         selectbackground=COLORS['primary'], selectforeground=COLORS['text_white'])
username_entry.pack(side="left", fill="x", expand=True, ipady=12)

# زر مسح اسم المستخدم
clear_username_btn = tk.Button(username_input_frame, text="🗑️ مسح",
                              font=('Arial', 12, 'bold'),
                              bg=COLORS['btn_danger'], fg=COLORS['text_white'],
                              relief="raised", bd=2, width=8, height=1,
                              cursor="hand2", activebackground=COLORS['error'],
                              activeforeground=COLORS['text_white'],
                              command=lambda: username_entry.delete(0, tk.END))
clear_username_btn.pack(side="right", padx=(12, 0), pady=2)

# حقل كلمة المرور
pass_frame = tk.Frame(fields_frame, bg=COLORS['bg_card'])
pass_frame.pack(fill="x", pady=12)

tk.Label(pass_frame, text=f"{ICONS['settings']} كلمة المرور:",
         font=('Arial', 14, 'bold'), bg=COLORS['bg_card'],
         fg=COLORS['primary']).pack(anchor="w", pady=(0, 8))

# إطار حقل كلمة المرور مع زر
password_input_frame = tk.Frame(pass_frame, bg=COLORS['bg_card'])
password_input_frame.pack(fill="x", pady=(5, 0))

password_entry = tk.Entry(password_input_frame, font=('Arial', 13), relief="solid", bd=2, show="*",
                         highlightthickness=2, highlightcolor=COLORS['primary'],
                         highlightbackground=COLORS['accent'], insertbackground=COLORS['primary'],
                         selectbackground=COLORS['primary'], selectforeground=COLORS['text_white'])
password_entry.pack(side="left", fill="x", expand=True, ipady=12)

# متغير لحالة إظهار كلمة المرور
password_visible = tk.BooleanVar(value=False)

# زر إظهار/إخفاء كلمة المرور
def toggle_password():
    if password_visible.get():
        password_entry.configure(show="*")
        show_password_btn.configure(text="👁️ إظهار", bg=COLORS['accent'])
        password_hint.configure(text="💡 اضغط على زر 'إظهار' لرؤية كلمة المرور")
        password_visible.set(False)
    else:
        password_entry.configure(show="")
        show_password_btn.configure(text="🙈 إخفاء", bg=COLORS['primary'])
        password_hint.configure(text="🔓 كلمة المرور مرئية الآن - اضغط 'إخفاء' لإخفائها")
        password_visible.set(True)

show_password_btn = tk.Button(password_input_frame, text="👁️ إظهار",
                             font=('Arial', 13, 'bold'),
                             bg=COLORS['accent'], fg=COLORS['text_white'],
                             relief="raised", bd=3, width=12, height=1,
                             cursor="hand2", activebackground=COLORS['info'],
                             activeforeground=COLORS['text_white'],
                             command=toggle_password)
show_password_btn.pack(side="right", padx=(15, 0), pady=2)

# تأثيرات hover للزر
def on_show_pass_enter(event):
    if password_visible.get():
        show_password_btn.configure(bg=COLORS['btn_success'], relief="solid", bd=3,
                                  text="🙈 إخفاء الآن", font=('Arial', 13, 'bold'))
    else:
        show_password_btn.configure(bg=COLORS['info'], relief="solid", bd=3,
                                  text="👁️ إظهار الآن", font=('Arial', 13, 'bold'))

def on_show_pass_leave(event):
    if password_visible.get():
        show_password_btn.configure(bg=COLORS['primary'], relief="raised", bd=2,
                                  text="🙈 إخفاء", font=('Arial', 12, 'bold'))
    else:
        show_password_btn.configure(bg=COLORS['accent'], relief="raised", bd=2,
                                  text="👁️ إظهار", font=('Arial', 12, 'bold'))

show_password_btn.bind("<Enter>", on_show_pass_enter)
show_password_btn.bind("<Leave>", on_show_pass_leave)

# تلميح لزر إظهار كلمة المرور
password_hint = tk.Label(pass_frame, text="💡 اضغط على زر 'إظهار' لرؤية كلمة المرور",
                        font=('Arial', 11, 'italic'), bg=COLORS['bg_card'],
                        fg=COLORS['text_light'])
password_hint.pack(anchor="w", pady=(8, 0))

# إضافة تأثير وميض للزر عند بدء التطبيق
def blink_password_button():
    current_bg = show_password_btn.cget('bg')
    if current_bg == COLORS['accent']:
        show_password_btn.configure(bg=COLORS['info'])
        root.after(500, lambda: show_password_btn.configure(bg=COLORS['accent']))
    root.after(3000, blink_password_button)  # كرر كل 3 ثوان

# بدء تأثير الوميض بعد ثانية من التشغيل
root.after(1000, blink_password_button)

# زر تسجيل الدخول المحسن
login_btn = tk.Button(login_container, text=f"{ICONS['success']} دخول إلى النظام",
                     command=login, bg=COLORS['btn_success'], fg=COLORS['text_white'],
                     font=('Arial', 16, 'bold'), width=25, height=2,
                     relief="raised", bd=3, cursor="hand2",
                     activebackground=COLORS['primary'],
                     activeforeground=COLORS['text_white'])
login_btn.pack(pady=30)

# معلومات تسجيل الدخول
info_frame = tk.Frame(login_container, bg=COLORS['light'], relief="solid", bd=1)
info_frame.pack(pady=15, padx=30, fill="x")

tk.Label(info_frame, text=f"{ICONS['info']} بيانات تسجيل الدخول:",
         font=FONTS['small'], bg=COLORS['light'], fg=COLORS['primary']).pack(pady=(8, 5))

# بيانات المدير
admin_frame = tk.Frame(info_frame, bg=COLORS['light'])
admin_frame.pack(pady=2)
tk.Label(admin_frame, text=f"{ICONS['settings']} المدير:",
         font=FONTS['small'], bg=COLORS['light'], fg=COLORS['text_primary']).pack(side="left")
tk.Label(admin_frame, text="admin / admin123",
         font=('Arial', 9, 'bold'), bg=COLORS['light'], fg=COLORS['primary']).pack(side="left", padx=(5, 0))

# بيانات المستخدم
user_frame = tk.Frame(info_frame, bg=COLORS['light'])
user_frame.pack(pady=(2, 8))
tk.Label(user_frame, text=f"{ICONS['users']} المستخدم:",
         font=FONTS['small'], bg=COLORS['light'], fg=COLORS['text_primary']).pack(side="left")
tk.Label(user_frame, text="user / user123",
         font=('Arial', 9, 'bold'), bg=COLORS['light'], fg=COLORS['accent']).pack(side="left", padx=(5, 0))

# ربط Enter
username_entry.bind("<Return>", lambda e: password_entry.focus())
password_entry.bind("<Return>", lambda e: login())

# تركيز على حقل اسم المستخدم
username_entry.focus()

# تشغيل التطبيق
root.mainloop()
